import cv2
import numpy as np
import os

# 全局参数
GRAY_MIN = 226
GRAY_MAX = 255
BRIGHTNESS = 0  # 亮度调节值 (-100 到 100)
CONTRAST = 100  # 对比度调节值 (50 到 300，对应0.5到3.0倍)
ROI = [796,233,1093,423]  # x1, y1, x2, y2
INVERT = False

# 控制是否读取阈值文件
USE_THRESHOLD_FILE = True

# 膨胀参数
DILATE_KERNEL_SIZE = 4
DILATE_ITERATIONS = 4

def load_parameters():
    """从阈值文件加载参数"""
    global GRAY_MIN, GRAY_MAX, BRIGHTNESS, CONTRAST, ROI

    if not USE_THRESHOLD_FILE:
        print("不读取阈值文件，使用全局参数")
        return

    try:
        # 检查文件是否存在
        if os.path.exists("thresholds_灰度.txt"):
            with open("thresholds_灰度.txt", "r") as f:
                lines = f.readlines()
                # 读取灰度阈值
                GRAY_MIN = int(lines[0].strip())
                GRAY_MAX = int(lines[1].strip())

                # 读取ROI
                ROI = [int(x) for x in lines[2].strip().split(",")]

                # 读取亮度值
                if len(lines) > 3:
                    BRIGHTNESS = int(lines[3].strip())

                # 读取对比度值
                if len(lines) > 4:
                    CONTRAST = int(lines[4].strip())

                print(f"已加载灰度参数: ({GRAY_MIN}-{GRAY_MAX})")
                print(f"ROI: {ROI}, 亮度: {BRIGHTNESS}, 对比度: {CONTRAST/100.0:.1f}")
        else:
            print("阈值文件不存在，使用默认参数")
    except Exception as e:
        print(f"加载参数时出错: {str(e)}")
        print("使用默认参数")

def adjust_brightness_contrast(image, brightness=0, contrast=1.0):
    """调节图像的亮度和对比度
    brightness: 亮度调节值 (-100 到 100)
    contrast: 对比度调节值 (0.5 到 3.0)
    """
    try:
        # 应用对比度和亮度调节
        # 公式: new_image = contrast * image + brightness
        adjusted = cv2.convertScaleAbs(image, alpha=contrast, beta=brightness)
        return adjusted
    except Exception as e:
        print(f"调节亮度对比度时出错: {str(e)}")
        return image

def analyze_laser_color(image, contour):
    """分析轮廓内部区域，简单直接地比较RGB通道值来判断是红激光还是绿激光
    参数:
        image: 输入图像
        contour: 轮廓点集
    返回: "RED", "GREEN" 或 "UNKNOWN"
    """
    # 创建轮廓掩码
    mask = np.zeros(image.shape[:2], dtype=np.uint8)
    cv2.drawContours(mask, [contour], 0, 255, -1)  # 填充轮廓内部为白色(255)

    # 使用掩码提取轮廓内的像素
    masked_image = cv2.bitwise_and(image, image, mask=mask)

    # 计算轮廓内的非零像素数量
    non_zero_pixels = cv2.countNonZero(mask)
    if non_zero_pixels == 0:
        return "UNKNOWN"

    # 获取各通道的总和，但只统计轮廓内部的像素
    b_sum = int(np.sum(masked_image[:, :, 0]))
    g_sum = int(np.sum(masked_image[:, :, 1]))
    r_sum = int(np.sum(masked_image[:, :, 2]))
    
    # 记录调试信息
    print(f"轮廓内部像素统计 - 红色总和: {r_sum}, 绿色总和: {g_sum}, 蓝色总和: {b_sum}")

    # 直接比较红绿通道的总和来判断激光类型
    if g_sum > r_sum - 3000:  # 绿色值显著大于红色
        return "GREEN"
    if r_sum > g_sum * 1.0:  # 红色值显著大于绿色
        return "RED"

    else:
        return "UNKNOWN"

def process_frame(frame):
    """使用灰度阈值处理帧并查找边缘"""
    # 检查空帧
    if frame is None or frame.size == 0:
        return None, None, None, None, None
    
    # 复制帧以避免修改原始数据
    frame = frame.copy()
    orig_frame = frame.copy()

    # 应用亮度和对比度调节
    contrast_factor = CONTRAST / 100.0  # 转换为0.5-3.0范围
    frame = adjust_brightness_contrast(frame, BRIGHTNESS, contrast_factor)

    roi_frame = None
    
    # 最终处理结果
    binary_result = None        # 二值化结果
    contour_result = None       # 轮廓结果
    edge_result = None          # 边缘检测结果
    rectangle_result = None     # 矩形框结果
    
    # 应用ROI
    if ROI is not None:
        x1, y1, x2, y2 = ROI
        height, width = frame.shape[:2]
        
        # 确保坐标在图像范围内
        x1 = max(0, min(x1, width-1))
        y1 = max(0, min(y1, height-1))
        x2 = max(0, min(x2, width-1))
        y2 = max(0, min(y2, height-1))
        
        # 检查ROI是否有效
        if x2 > x1 and y2 > y1:
            # 提取ROI，不进行缩放操作
            roi_frame = orig_frame[y1:y2, x1:x2].copy()
            
            # 在原始图像上绘制ROI区域边框
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 对ROI区域进行处理
            if roi_frame.size > 0:
                # 转换为灰度图
                gray_roi = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2GRAY)
                
                # 应用灰度阈值处理
                binary = cv2.inRange(gray_roi, GRAY_MIN, GRAY_MAX)
                
                # 如果启用反转，则应用反转
                if INVERT:
                    binary = cv2.bitwise_not(binary)
                
                # 对二值化结果进行膨胀 - 始终应用膨胀
                kernel = np.ones((DILATE_KERNEL_SIZE, DILATE_KERNEL_SIZE), np.uint8)
                binary = cv2.dilate(binary, kernel, iterations=DILATE_ITERATIONS)
                
                # 创建二值化结果的彩色版本
                binary_result = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
                
                # 寻找边缘
                edges = cv2.Canny(binary, 50, 150)
                edge_result = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
                
                # 寻找轮廓
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # 创建轮廓显示图像
                contour_result = roi_frame.copy()
                cv2.drawContours(contour_result, contours, -1, (0, 255, 255), 2)  # 黄色线条
                
                # 创建矩形框显示图像
                rectangle_result = roi_frame.copy()
                
                # 对每个轮廓画出外接矩形
                for contour in contours:
                    # 仅处理合适面积的轮廓（排除太小和太大的区域）
                    contour_area = cv2.contourArea(contour)
                    if 5 < contour_area < 500:  # 激光点面积范围：5-500像素
                        # 获取原始外接矩形
                        orig_x, orig_y, orig_w, orig_h = cv2.boundingRect(contour)

                        # 缩小矩形尺寸（保留中心30%的区域）
                        shrink_factor = 0.3
                        new_w = max(3, int(orig_w * shrink_factor))  # 至少3像素宽
                        new_h = max(3, int(orig_h * shrink_factor))  # 至少3像素高

                        # 计算缩小后的矩形位置（保持中心不变）
                        x = orig_x + (orig_w - new_w) // 2
                        y = orig_y + (orig_h - new_h) // 2
                        w = new_w
                        h = new_h

                        # 分析激光颜色 - 只统计轮廓内部的像素
                        laser_type = analyze_laser_color(roi_frame, contour)

                        # 计算激光点的中心坐标（相对于ROI）
                        center_x = x + w // 2
                        center_y = y + h // 2

                        # 转换为全局坐标（相对于整个图像）
                        global_x = ROI[0] + center_x
                        global_y = ROI[1] + center_y

                        # 根据激光类型设置矩形颜色并输出坐标
                        rect_color = (0, 255, 255)  # 默认黄色
                        if laser_type == "RED":
                            rect_color = (0, 0, 255)  # 红色激光用红色矩形
                            print(f"🔴 红色激光点坐标: ({global_x}, {global_y}) - ROI内坐标: ({center_x}, {center_y}) - 面积: {contour_area:.0f}")
                        elif laser_type == "GREEN":
                            rect_color = (0, 255, 0)  # 绿色激光用绿色矩形
                            print(f"🟢 绿色激光点坐标: ({global_x}, {global_y}) - ROI内坐标: ({center_x}, {center_y}) - 面积: {contour_area:.0f}")
                        
                        # 绘制矩形
                        cv2.rectangle(rectangle_result, (x, y), (x+w, y+h), rect_color, 2)

                        # 在矩形中心画一个十字
                        cv2.drawMarker(rectangle_result, (center_x, center_y), (0, 0, 255),
                                       cv2.MARKER_CROSS, 10, 2)

                        # 显示激光类型和坐标文本
                        coord_text = f"{laser_type}({global_x},{global_y})"
                        cv2.putText(rectangle_result, coord_text, (x, y-5),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.4, rect_color, 1)

                        # 在原图上绘制矩形和坐标（转换为全局坐标）
                        global_rect_x = ROI[0] + x
                        global_rect_y = ROI[1] + y
                        cv2.rectangle(frame, (global_rect_x, global_rect_y),
                                     (global_rect_x + w, global_rect_y + h), rect_color, 2)
                        cv2.putText(frame, coord_text, (global_rect_x, global_rect_y-5),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, rect_color, 1)
    else:
        # 无有效ROI时，直接处理整个帧
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 应用灰度阈值处理
        binary = cv2.inRange(gray, GRAY_MIN, GRAY_MAX)
        
        # 如果启用反转，则应用反转
        if INVERT:
            binary = cv2.bitwise_not(binary)
        
        # 对二值化结果进行膨胀 - 始终应用膨胀
        kernel = np.ones((DILATE_KERNEL_SIZE, DILATE_KERNEL_SIZE), np.uint8)
        binary = cv2.dilate(binary, kernel, iterations=DILATE_ITERATIONS)
        
        # 创建二值化结果的彩色版本
        binary_result = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
        
        # 寻找边缘
        edges = cv2.Canny(binary, 50, 150)
        edge_result = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
        
        # 寻找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 创建轮廓显示图像
        contour_result = frame.copy()
        cv2.drawContours(contour_result, contours, -1, (0, 255, 255), 2)  # 黄色线条
        
        # 创建矩形框显示图像
        rectangle_result = frame.copy()
        
        # 对每个轮廓画出外接矩形
        for contour in contours:
            # 仅处理合适面积的轮廓（排除太小和太大的区域）
            contour_area = cv2.contourArea(contour)
            if 5 < contour_area < 500:  # 激光点面积范围：5-500像素
                # 获取原始外接矩形
                orig_x, orig_y, orig_w, orig_h = cv2.boundingRect(contour)

                # 缩小矩形尺寸（保留中心30%的区域）
                shrink_factor = 0.3
                new_w = max(3, int(orig_w * shrink_factor))  # 至少3像素宽
                new_h = max(3, int(orig_h * shrink_factor))  # 至少3像素高

                # 计算缩小后的矩形位置（保持中心不变）
                x = orig_x + (orig_w - new_w) // 2
                y = orig_y + (orig_h - new_h) // 2
                w = new_w
                h = new_h

                # 分析激光颜色 - 只统计轮廓内部的像素
                laser_type = analyze_laser_color(frame, contour)

                # 计算激光点的中心坐标
                center_x = x + w // 2
                center_y = y + h // 2

                # 根据激光类型设置矩形颜色并输出坐标
                rect_color = (0, 255, 255)  # 默认黄色
                if laser_type == "RED":
                    rect_color = (0, 0, 255)  # 红色激光用红色矩形
                    print(f"🔴 红色激光点坐标: ({center_x}, {center_y}) - 全图模式 - 面积: {contour_area:.0f}")
                elif laser_type == "GREEN":
                    rect_color = (0, 255, 0)  # 绿色激光用绿色矩形
                    print(f"🟢 绿色激光点坐标: ({center_x}, {center_y}) - 全图模式 - 面积: {contour_area:.0f}")
                
                # 绘制矩形
                cv2.rectangle(frame, (x, y), (x+w, y+h), rect_color, 2)

                # 在矩形中心画一个十字
                cv2.drawMarker(frame, (center_x, center_y), (0, 0, 255),
                               cv2.MARKER_CROSS, 10, 2)

                # 显示激光类型和坐标文本
                coord_text = f"{laser_type}({center_x},{center_y})"
                cv2.putText(frame, coord_text, (x, y-5),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.4, rect_color, 1)
    
    return frame, binary_result, edge_result, contour_result, rectangle_result

def main():
    # 添加全局变量声明
    global USE_THRESHOLD_FILE
    
    # 加载参数
    load_parameters()
    
    # 打开摄像头
    cap = cv2.VideoCapture(0)

    if not cap.isOpened():
        print("错误: 无法打开摄像头")
        return

    # 获取摄像头尺寸
    cam_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    cam_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    # 检查摄像头尺寸是否有效
    if cam_width <= 0 or cam_height <= 0:
        print(f"警告: 摄像头尺寸无效 ({cam_width}x{cam_height})，使用默认尺寸")
        cam_width = 640
        cam_height = 480

    print(f"摄像头尺寸: {cam_width}x{cam_height}")

    # 计算窗口尺寸，保持视频原始比例
    # 假设设计窗口宽度480，高度根据比例计算
    display_width = 480
    # 确保不会除零
    if cam_width > 0:
        display_height = int(display_width * cam_height / cam_width)
    else:
        display_height = 360  # 默认高度
    
    # 不再设置硬件曝光度，改用软件调节亮度对比度
    print(f"使用软件亮度对比度调节: 亮度={BRIGHTNESS}, 对比度={CONTRAST/100.0:.1f}")
    
    # 创建自定义大小的窗口
    cv2.namedWindow("Original", cv2.WINDOW_NORMAL)
    cv2.namedWindow("Binary", cv2.WINDOW_NORMAL)
    cv2.namedWindow("Edges", cv2.WINDOW_NORMAL)
    cv2.namedWindow("Contours", cv2.WINDOW_NORMAL)
    cv2.namedWindow("Rectangles", cv2.WINDOW_NORMAL)
    
    # 调整窗口位置和大小
    cv2.moveWindow("Original", 50, 50)
    cv2.moveWindow("Binary", 50, 50 + display_height + 40)
    cv2.moveWindow("Edges", 50 + display_width + 20, 50)
    cv2.moveWindow("Contours", 50 + display_width + 20, 50 + display_height + 40)
    cv2.moveWindow("Rectangles", 50 + 2*(display_width + 20), 50)
    
    # 调整窗口大小
    cv2.resizeWindow("Original", display_width, display_height)
    cv2.resizeWindow("Binary", display_width, display_height)
    cv2.resizeWindow("Edges", display_width, display_height)
    cv2.resizeWindow("Contours", display_width, display_height)
    cv2.resizeWindow("Rectangles", display_width, display_height)
    
    # 构建参数文本，包含控制参数信息
    mode_text = "使用阈值文件" if USE_THRESHOLD_FILE else "使用默认参数"
    dilate_text = f"膨胀: 核大小={DILATE_KERNEL_SIZE}, 迭代={DILATE_ITERATIONS}"
    param_text = f"Gray: ({GRAY_MIN}-{GRAY_MAX}) 亮度:{BRIGHTNESS} 对比度:{CONTRAST/100.0:.1f} Mode:{mode_text} {dilate_text}"
    print(param_text)
    
    while True:
        # 读取帧
        ret, frame = cap.read()
        if not ret:
            print("无法从摄像头读取")
            break
            
        # 处理图像
        original, binary, edges, contours, rectangles = process_frame(frame)
        
        if original is None:
            print("处理帧时出错")
            break
            
        # 添加参数文本到原始图像
        cv2.putText(original, param_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        # 显示所有处理阶段的图像
        cv2.imshow("Original", original)
        
        if binary is not None:
            cv2.imshow("Binary", binary)
            
        if edges is not None:
            cv2.imshow("Edges", edges)
            
        if contours is not None:
            cv2.imshow("Contours", contours)
            
        if rectangles is not None:
            cv2.imshow("Rectangles", rectangles)
        
        # 等待按键
        key = cv2.waitKey(30)
        if key == 27:  # ESC键退出
            break
        elif key == ord('m') or key == ord('M'):  # 按M键切换模式
            USE_THRESHOLD_FILE = not USE_THRESHOLD_FILE
            mode_text = "使用阈值文件" if USE_THRESHOLD_FILE else "使用默认参数"
            param_text = f"Gray: ({GRAY_MIN}-{GRAY_MAX}) 亮度:{BRIGHTNESS} 对比度:{CONTRAST/100.0:.1f} Mode:{mode_text} {dilate_text}"
            print(f"切换模式: {mode_text}")
    
    # 释放资源
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main() 