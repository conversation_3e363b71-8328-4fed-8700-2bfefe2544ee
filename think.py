import cv2
import numpy as np
import json
import os
import time
from serial_comm import SerialComm

# 创建简洁的调试窗口
def create_debug_window():
    cv2.namedWindow('Debug Controls', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('Debug Controls', 350, 200)

    # 创建滑动条 - 简洁版本
    cv2.createTrackbar('Min Area', 'Debug Controls', 100, 5000, lambda x: None)
    cv2.createTrackbar('Max Area', 'Debug Controls', 300, 1000, lambda x: None)
    cv2.createTrackbar('Threshold', 'Debug Controls', 46, 255, lambda x: None)
    cv2.createTrackbar('Epsilon', 'Debug Controls', 3, 10, lambda x: None)
    cv2.createTrackbar('Scale', 'Debug Controls', 50, 100, lambda x: None)

    # 创建一个空白图像作为控制面板背景
    control_panel = np.zeros((200, 350, 3), dtype=np.uint8)
    cv2.putText(control_panel, "Parameter Controls", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(control_panel, "Adjust sliders to tune detection", (10, 60),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
    cv2.imshow('Debug Controls', control_panel)

    print("参数调节窗口已创建")

# 参数配置文件名
CONFIG_FILE = "rectangle_detection_params.json"

# 矩形跟踪和坐标系管理类
class RectangleTracker:
    def __init__(self, lost_timeout=3.0):
        self.reference_rect = None          # 参考矩形（第一次检测到的矩形）
        self.coordinate_origin = None       # 坐标系原点（左上角）
        self.is_tracking = False           # 是否正在跟踪
        self.last_detection_time = 0       # 最后检测到矩形的时间
        self.lost_timeout = lost_timeout   # 丢失超时时间（秒）
        self.lost_start_time = None        # 开始丢失的时间

    def update(self, rectangles):
        """更新矩形跟踪状态"""
        current_time = time.time()

        if rectangles:
            # 检测到矩形
            self.last_detection_time = current_time
            self.lost_start_time = None

            if not self.is_tracking:
                # 第一次检测到矩形，设置参考坐标系
                self.set_reference_rectangle(rectangles[0])
                self.is_tracking = True

        else:
            # 没有检测到矩形
            if self.is_tracking:
                if self.lost_start_time is None:
                    self.lost_start_time = current_time

                lost_duration = current_time - self.lost_start_time
                if lost_duration >= self.lost_timeout:
                    # 超时，重置跟踪
                    self.reset_tracking()

    def set_reference_rectangle(self, rect_data):
        """设置参考矩形和坐标系原点"""
        approx, area = rect_data
        self.reference_rect = approx.copy()

        # 找到矩形的边界
        points = approx.reshape(-1, 2)
        min_x = np.min(points[:, 0])
        min_y = np.min(points[:, 1])
        max_x = np.max(points[:, 0])
        max_y = np.max(points[:, 1])

        # 设置坐标原点为矩形左上角向外扩展50像素的位置
        # 这样确保所有检测点都在正坐标范围内
        offset = 50
        self.coordinate_origin = np.array([min_x - offset, min_y - offset])

        print(f"坐标系原点设置为: ({self.coordinate_origin[0]}, {self.coordinate_origin[1]})")
        print(f"矩形范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")



    def reset_tracking(self):
        """重置跟踪状态"""
        self.reference_rect = None
        self.coordinate_origin = None
        self.is_tracking = False
        self.lost_start_time = None

    def convert_to_local_coordinates(self, global_point):
        """将全局坐标转换为以坐标原点为基准的局部坐标（确保为正数）"""
        if self.coordinate_origin is None:
            return global_point

        local_x = global_point[0] - self.coordinate_origin[0]
        local_y = global_point[1] - self.coordinate_origin[1]

        # 确保坐标为正数，如果为负数则自动调整原点
        if local_x < 0 or local_y < 0:
            # 动态调整原点以确保坐标为正
            if local_x < 0:
                self.coordinate_origin[0] = global_point[0] - 10  # 留10像素边距
                local_x = 10
            if local_y < 0:
                self.coordinate_origin[1] = global_point[1] - 10  # 留10像素边距
                local_y = 10

            print(f"坐标原点已自动调整为: ({self.coordinate_origin[0]}, {self.coordinate_origin[1]})")

        return (int(local_x), int(local_y))

    def calculate_diagonal_intersection(self, rect_points):
        """计算矩形对角线交点"""
        if len(rect_points) != 4:
            return None

        # 对顶点排序：左上、右上、右下、左下
        points = rect_points.reshape(-1, 2)
        # 按x+y排序找到左上和右下
        sum_coords = points.sum(axis=1)
        top_left_idx = np.argmin(sum_coords)
        bottom_right_idx = np.argmax(sum_coords)

        # 按x-y排序找到右上和左下
        diff_coords = points[:, 0] - points[:, 1]
        top_right_idx = np.argmin(diff_coords)
        bottom_left_idx = np.argmax(diff_coords)

        top_left = points[top_left_idx]
        bottom_right = points[bottom_right_idx]
        top_right = points[top_right_idx]
        bottom_left = points[bottom_left_idx]

        # 计算两条对角线的交点
        # 对角线1: 左上到右下
        # 对角线2: 右上到左下

        # 使用线段交点公式
        x1, y1 = top_left
        x2, y2 = bottom_right
        x3, y3 = top_right
        x4, y4 = bottom_left

        # 计算交点
        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        if abs(denom) < 1e-10:
            # 平行线，返回中心点
            center_x = (x1 + x2 + x3 + x4) / 4
            center_y = (y1 + y2 + y3 + y4) / 4
        else:
            t = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
            center_x = x1 + t*(x2-x1)
            center_y = y1 + t*(y2-y1)

        return (int(center_x), int(center_y))

    def get_status_text(self):
        """获取状态文本"""
        if not self.is_tracking:
            return "等待矩形检测..."
        elif self.lost_start_time is not None:
            lost_duration = time.time() - self.lost_start_time
            return f"矩形丢失: {lost_duration:.1f}/{self.lost_timeout}s"
        else:
            return "矩形跟踪中"

# 保存参数到文件
def save_params():
    params = {
        'min_area': cv2.getTrackbarPos('Min Area', 'Debug Controls'),
        'max_area': cv2.getTrackbarPos('Max Area', 'Debug Controls'),
        'threshold': cv2.getTrackbarPos('Threshold', 'Debug Controls'),
        'epsilon': cv2.getTrackbarPos('Epsilon', 'Debug Controls'),
        'scale': cv2.getTrackbarPos('Scale', 'Debug Controls')
    }

    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(params, f, indent=2)
        print(f"参数已保存到 {CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"保存参数失败: {e}")
        return False

# 从文件加载参数
def load_params():
    if not os.path.exists(CONFIG_FILE):
        print(f"配置文件 {CONFIG_FILE} 不存在，使用默认参数")
        return False

    try:
        with open(CONFIG_FILE, 'r') as f:
            params = json.load(f)

        # 设置滑动条位置
        cv2.setTrackbarPos('Min Area', 'Debug Controls', params.get('min_area', 100))
        cv2.setTrackbarPos('Max Area', 'Debug Controls', params.get('max_area', 300))
        cv2.setTrackbarPos('Threshold', 'Debug Controls', params.get('threshold', 46))
        cv2.setTrackbarPos('Epsilon', 'Debug Controls', params.get('epsilon', 3))
        cv2.setTrackbarPos('Scale', 'Debug Controls', params.get('scale', 50))

        print(f"参数已从 {CONFIG_FILE} 加载")
        return True
    except Exception as e:
        print(f"加载参数失败: {e}")
        return False

# 获取调试参数
def get_debug_params():
    min_area = cv2.getTrackbarPos('Min Area', 'Debug Controls')
    max_area = cv2.getTrackbarPos('Max Area', 'Debug Controls') * 100  # 乘以100
    threshold = cv2.getTrackbarPos('Threshold', 'Debug Controls')
    epsilon_factor = cv2.getTrackbarPos('Epsilon', 'Debug Controls') / 100.0  # 除以100
    scale = cv2.getTrackbarPos('Scale', 'Debug Controls')

    return min_area, max_area, threshold, epsilon_factor, scale

# 打开摄像头
cap = cv2.VideoCapture(0)

if not cap.isOpened():
    print("无法打开摄像头，请检查设备连接。")
else:
    # 创建调试窗口
    create_debug_window()

    # 尝试加载保存的参数
    load_params()

    # 初始化矩形跟踪器
    tracker = RectangleTracker(lost_timeout=3.0)  # 3秒超时

    # 初始化串口通信
    serial_comm = SerialComm(port='/dev/ttyTHS0', baudrate=115200, enable=True)
    serial_comm.init()

    print("\n=== 矩形检测程序 ===")
    print("窗口说明:")
    print("- Rectangle Detection Result: 检测结果窗口")
    print("- Debug Controls: 参数调节窗口")
    print("功能:")
    print("- 自动设置坐标系原点为矩形左上角")
    print("- 显示局部坐标 L(x,y)")
    print("- 矩形丢失3秒后重置跟踪")
    print("- 串口发送矩形中点和激光点坐标")
    print("按 'h' 查看详细帮助, 'c' 切换串口功能")
    print("===================\n")

    while True:
        # 读取一帧图像
        ret, frame = cap.read()

        if not ret:
            print("无法读取帧，请检查摄像头。")
            break

        # 获取调试参数
        min_contour_area, max_contour_area, threshold_val, epsilon_factor, scale_percent = get_debug_params()

        # 使用调试参数进行缩放
        width = int(frame.shape[1] * scale_percent / 100)
        height = int(frame.shape[0] * scale_percent / 100)
        dim = (width, height)

        # 缩放图像
        resized_image = cv2.resize(frame, dim, interpolation=cv2.INTER_AREA)
        # 转换为灰度图像
        gray_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2GRAY)
        # 使用调试参数进行二值化处理
        _, binary_image = cv2.threshold(gray_image, threshold_val, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # 指定要显示的边数
        target_sides = 4
        approximated_contours = []
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            if area > min_contour_area and area < max_contour_area:
                # 使用调试参数进行多边形逼近
                epsilon = epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                if len(approx) == target_sides:
                    approximated_contours.append((approx, area))

        # 按面积排序，面积大的为外框，面积小的为内框
        approximated_contours.sort(key=lambda x: x[1], reverse=True)

        # 更新跟踪器状态
        tracker.update(approximated_contours)

        # 在原始缩放图像上绘制指定边数的轮廓
        contour_image = resized_image.copy()

        # 显示简洁的状态信息
        status_text = tracker.get_status_text()
        status_color = (0, 255, 0) if tracker.is_tracking else (255, 255, 0)
        cv2.putText(contour_image, f"Status: {status_text}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, status_color, 2)

        # 显示矩形数量
        rect_count_text = f"Rectangles: {len(approximated_contours)}"
        cv2.putText(contour_image, rect_count_text, (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # 显示按键提示
        help_text = "Press 'h' for help, 't' to reset tracking"
        cv2.putText(contour_image, help_text, (10, contour_image.shape[0] - 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

        # 如果有坐标原点，绘制坐标系
        if tracker.coordinate_origin is not None:
            origin = tracker.coordinate_origin

            # 检查原点是否在图像范围内
            if 0 <= origin[0] < contour_image.shape[1] and 0 <= origin[1] < contour_image.shape[0]:
                # 绘制坐标原点
                cv2.circle(contour_image, tuple(origin.astype(int)), 8, (0, 0, 255), -1)
                cv2.putText(contour_image, "Origin(0,0)", (int(origin[0])+10, int(origin[1])-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

                # 绘制坐标轴
                axis_length = 50
                cv2.arrowedLine(contour_image, tuple(origin.astype(int)),
                               (int(origin[0])+axis_length, int(origin[1])), (0, 0, 255), 2)  # X轴
                cv2.arrowedLine(contour_image, tuple(origin.astype(int)),
                               (int(origin[0]), int(origin[1])+axis_length), (0, 255, 0), 2)  # Y轴
                cv2.putText(contour_image, "X", (int(origin[0])+axis_length+5, int(origin[1])),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
                cv2.putText(contour_image, "Y", (int(origin[0]), int(origin[1])+axis_length+15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            else:
                # 如果原点在图像外，在图像边缘显示提示
                cv2.putText(contour_image, "Origin outside image", (10, contour_image.shape[0]-40),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
        
        # 如果检测到至少两个矩形（外框和内框）
        if len(approximated_contours) >= 2:
            outer_rect = approximated_contours[0][0]  # 外框
            inner_rect = approximated_contours[1][0]  # 内框

            # 绘制外框和内框轮廓
            cv2.drawContours(contour_image, [outer_rect], -1, (0, 0, 255), 2)  # 红色外框
            cv2.drawContours(contour_image, [inner_rect], -1, (0, 255, 0), 2)  # 绿色内框

            # 为外框顶点添加坐标
            for i, point in enumerate(outer_rect):
                x, y = point[0]
                cv2.circle(contour_image, (x, y), 5, (0, 0, 255), -1)
                cv2.putText(contour_image, f"O{i}({x},{y})", (x+10, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)

            # 为内框顶点添加坐标
            for i, point in enumerate(inner_rect):
                x, y = point[0]
                cv2.circle(contour_image, (x, y), 5, (0, 255, 0), -1)
                cv2.putText(contour_image, f"I{i}({x},{y})", (x+10, y+15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

            # 对顶点进行排序，确保对应关系正确
            def sort_vertices(vertices):
                # 计算质心
                center_x = sum([p[0][0] for p in vertices]) / 4
                center_y = sum([p[0][1] for p in vertices]) / 4

                # 按角度排序（从左上角开始，顺时针）
                import math
                def angle_from_center(point):
                    return math.atan2(point[0][1] - center_y, point[0][0] - center_x)

                return sorted(vertices, key=angle_from_center)

            # 排序顶点
            outer_sorted = sort_vertices(outer_rect)
            inner_sorted = sort_vertices(inner_rect)
            
            # 对内部小矩形计算对角线交点
            inner_center_point = tracker.calculate_diagonal_intersection(inner_rect)
            if inner_center_point and tracker.is_tracking:
                # 绘制内部矩形对角线交点
                cv2.circle(contour_image, inner_center_point, 8, (255, 0, 255), -1)  # 紫色圆点

                # 转换为局部坐标
                local_center = tracker.convert_to_local_coordinates(inner_center_point)

                # 显示交点坐标
                cv2.putText(contour_image, f"Inner Center L{local_center}",
                           (inner_center_point[0]+15, inner_center_point[1]-15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 2)

                # 打印内部矩形对角线交点坐标到控制台
                print(f"内部矩形对角线交点: 全局({inner_center_point[0]}, {inner_center_point[1]}) -> 局部{local_center}")

                # 将内部矩形对角线交点作为激光点发送到串口
                serial_comm.send_red_laser_point(inner_center_point)

            # 转换特定坐标点 (150, 115)
            if tracker.is_tracking:
                specific_global = (150, 115)
                specific_local = tracker.convert_to_local_coordinates(specific_global)

                # 在图像上标注这个特定点
                cv2.circle(contour_image, specific_global, 6, (0, 255, 255), -1)  # 黄色圆点
                cv2.putText(contour_image, f"(150,115) L{specific_local}",
                           (specific_global[0]+10, specific_global[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

                # 打印特定坐标转换到控制台
                print(f"特定坐标(150,115): 全局(150, 115) -> 局部{specific_local}")

            # 计算对应顶点的中点并收集用于串口发送
            midpoints = []
            for i in range(4):
                outer_x, outer_y = outer_sorted[i][0]
                inner_x, inner_y = inner_sorted[i][0]

                # 计算中点坐标
                mid_x = (outer_x + inner_x) // 2
                mid_y = (outer_y + inner_y) // 2
                midpoints.append((mid_x, mid_y))

                # 绘制中点十字标记
                cross_size = 10
                cv2.line(contour_image, (mid_x-cross_size, mid_y), (mid_x+cross_size, mid_y), (255, 255, 0), 2)
                cv2.line(contour_image, (mid_x, mid_y-cross_size), (mid_x, mid_y+cross_size), (255, 255, 0), 2)

                # 转换为局部坐标
                global_point = (mid_x, mid_y)
                local_point = tracker.convert_to_local_coordinates(global_point)

                # 显示全局和局部坐标
                coord_text = f"G({mid_x},{mid_y}) L{local_point}"
                cv2.putText(contour_image, coord_text, (mid_x+15, mid_y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

            # 发送矩形中点坐标到串口
            if len(midpoints) == 4:
                serial_comm.send_rect_midpoints(midpoints)


        
        else:
            # 如果只有一个矩形，正常绘制并显示坐标
            for i, (approx, area) in enumerate(approximated_contours):
                color = (0, 0, 255) if i == 0 else (0, 255, 0)
                cv2.drawContours(contour_image, [approx], -1, color, 2)

                # 单个矩形情况下也进行相同处理
                if i == 0:  # 只对第一个（最大的）矩形处理
                    # 计算对角线交点
                    center_point = tracker.calculate_diagonal_intersection(approx)
                    if center_point and tracker.is_tracking:
                        # 绘制对角线交点
                        cv2.circle(contour_image, center_point, 8, (255, 0, 255), -1)  # 紫色圆点

                        # 转换为局部坐标
                        local_center = tracker.convert_to_local_coordinates(center_point)

                        # 显示交点坐标
                        cv2.putText(contour_image, f"Center L{local_center}",
                                   (center_point[0]+15, center_point[1]-15),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 2)

                        # 打印对角线交点坐标到控制台
                        print(f"矩形对角线交点: 全局({center_point[0]}, {center_point[1]}) -> 局部{local_center}")

                        # 将矩形对角线交点作为激光点发送到串口
                        serial_comm.send_red_laser_point(center_point)

                    # 转换特定坐标点 (150, 115)
                    if tracker.is_tracking:
                        specific_global = (150, 115)
                        specific_local = tracker.convert_to_local_coordinates(specific_global)

                        # 在图像上标注这个特定点
                        cv2.circle(contour_image, specific_global, 6, (0, 255, 255), -1)  # 黄色圆点
                        cv2.putText(contour_image, f"(150,115) L{specific_local}",
                                   (specific_global[0]+10, specific_global[1]-10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

                        # 打印特定坐标转换到控制台
                        print(f"特定坐标(150,115): 全局(150, 115) -> 局部{specific_local}")

                # 为每个顶点添加坐标标注
                for j, point in enumerate(approx):
                    x, y = point[0]
                    # 绘制顶点圆点
                    cv2.circle(contour_image, (x, y), 5, color, -1)

                    # 转换为局部坐标
                    global_point = (x, y)
                    local_point = tracker.convert_to_local_coordinates(global_point)

                    # 显示局部坐标
                    cv2.putText(contour_image, f"L{local_point}", (x+10, y-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)


        # 在图像上显示检测统计信息
        info_text = f"检测到 {len(approximated_contours)} 个矩形"
        cv2.putText(contour_image, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # 显示面积信息
        if approximated_contours:
            area_text = f"最大面积: {approximated_contours[0][1]:.0f}"
            cv2.putText(contour_image, area_text, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # 只显示标注完成的结果图像
        cv2.imshow('Rectangle Detection Result', contour_image)

        # 按键处理
        key = cv2.waitKey(1) & 0xFF
        if key == 27:  # Esc键退出
            break
        elif key == ord('r'):  # 'r'键重置参数
            cv2.setTrackbarPos('Min Area', 'Debug Controls', 100)
            cv2.setTrackbarPos('Max Area', 'Debug Controls', 300)
            cv2.setTrackbarPos('Threshold', 'Debug Controls', 46)
            cv2.setTrackbarPos('Epsilon', 'Debug Controls', 3)
            cv2.setTrackbarPos('Scale', 'Debug Controls', 50)
        elif key == ord('s'):  # 's'键保存参数
            save_params()
        elif key == ord('l'):  # 'l'键重新加载参数
            load_params()
        elif key == ord('t'):  # 't'键重置跟踪
            tracker.reset_tracking()
        elif key == ord('c'):  # 'c'键切换串口功能
            serial_comm.enable = not serial_comm.enable
            status = "启用" if serial_comm.enable else "禁用"
            print(f"串口功能已{status}")
        elif key == ord('h'):  # 'h'键显示帮助
            print("\n=== 调试帮助 ===")
            print("按键功能:")
            print("  Esc - 退出程序")
            print("  r - 重置所有参数")
            print("  s - 保存当前参数")
            print("  l - 重新加载参数")
            print("  t - 重置矩形跟踪")
            print("  c - 切换串口功能启用/禁用")
            print("  h - 显示此帮助")
            print("坐标系功能:")
            print("- 第一次检测到矩形时，自动设置左上角为原点(0,0)")
            print("- 显示全局坐标G(x,y)和局部坐标L(x,y)")
            print("- 矩形丢失超过3秒后自动重置跟踪")
            print("串口功能:")
            print("- 发送矩形中点坐标 (500ms间隔)")
            print("- 发送内部矩形对角线交点作为激光点 (实时)")
            print("- 数据包格式: [帧头][指令][长度][数据][校验]")
            print("调节滑动条来改变检测参数:")
            print("- 如果检测不到矩形，降低Min Area，提高Max Area")
            print("- 如果二值化效果不好，调节Threshold")
            print("- 如果矩形不规则，调节Epsilon")
            print("参数会自动保存到 rectangle_detection_params.json")
            print("================\n")

    # 程序退出时自动保存参数
    save_params()

    # 关闭串口连接
    serial_comm.close()

    # 释放摄像头并关闭所有窗口
    cap.release()
    cv2.destroyAllWindows()
    
