#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化矩形检测程序
功能：检测矩形，计算内部矩形对角线交点，发送原始坐标到下位机
"""

import cv2
import numpy as np
import json
import os
from serial_comm import SerialComm

# 参数配置文件名
CONFIG_FILE = "simple_rect_params.json"

# 保存参数到文件
def save_params():
    params = {
        'min_area': cv2.getTrackbarPos('Min Area', 'Debug Controls'),
        'max_area': cv2.getTrackbarPos('Max Area', 'Debug Controls'),
        'threshold': cv2.getTrackbarPos('Threshold', 'Debug Controls'),
        'epsilon': cv2.getTrackbarPos('Epsilon', 'Debug Controls'),
        'scale': cv2.getTrackbarPos('Scale', 'Debug Controls')
    }

    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(params, f, indent=2)
        print(f"参数已保存到 {CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"保存参数失败: {e}")
        return False

# 从文件加载参数
def load_params():
    if not os.path.exists(CONFIG_FILE):
        print(f"配置文件 {CONFIG_FILE} 不存在，使用默认参数")
        return False

    try:
        with open(CONFIG_FILE, 'r') as f:
            params = json.load(f)

        # 设置滑动条位置
        cv2.setTrackbarPos('Min Area', 'Debug Controls', params.get('min_area', 100))
        cv2.setTrackbarPos('Max Area', 'Debug Controls', params.get('max_area', 300))
        cv2.setTrackbarPos('Threshold', 'Debug Controls', params.get('threshold', 46))
        cv2.setTrackbarPos('Epsilon', 'Debug Controls', params.get('epsilon', 3))
        cv2.setTrackbarPos('Scale', 'Debug Controls', params.get('scale', 50))

        print(f"参数已从 {CONFIG_FILE} 加载")
        return True
    except Exception as e:
        print(f"加载参数失败: {e}")
        return False

# 创建调试窗口和滑动条
def create_debug_window():
    cv2.namedWindow('Debug Controls', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('Debug Controls', 350, 200)

    # 创建滑动条
    cv2.createTrackbar('Min Area', 'Debug Controls', 100, 5000, lambda x: None)
    cv2.createTrackbar('Max Area', 'Debug Controls', 300, 1000, lambda x: None)
    cv2.createTrackbar('Threshold', 'Debug Controls', 46, 255, lambda x: None)
    cv2.createTrackbar('Epsilon', 'Debug Controls', 3, 10, lambda x: None)
    cv2.createTrackbar('Scale', 'Debug Controls', 50, 100, lambda x: None)

    # 创建控制面板背景
    control_panel = np.zeros((200, 350, 3), dtype=np.uint8)
    cv2.putText(control_panel, "Parameter Controls", (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(control_panel, "Adjust sliders to tune detection", (10, 60),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
    cv2.imshow('Debug Controls', control_panel)

# 获取调试参数
def get_debug_params():
    min_area = cv2.getTrackbarPos('Min Area', 'Debug Controls')
    max_area = cv2.getTrackbarPos('Max Area', 'Debug Controls') * 100  # 乘以100
    threshold = cv2.getTrackbarPos('Threshold', 'Debug Controls')
    epsilon_factor = cv2.getTrackbarPos('Epsilon', 'Debug Controls') / 100.0  # 除以100
    scale = cv2.getTrackbarPos('Scale', 'Debug Controls')

    return min_area, max_area, threshold, epsilon_factor, scale

class SimpleRectDetector:
    def __init__(self):
        # 串口通信
        self.serial_comm = SerialComm(port='/dev/ttyTHS0', baudrate=115200, enable=True)
        self.serial_comm.init()

        # 创建调试窗口
        create_debug_window()

        # 尝试加载保存的参数
        load_params()

        print("矩形检测程序启动（恢复完整功能）")
        print("功能：检测双矩形，发送内部矩形对角线交点原始坐标")
        print("窗口说明:")
        print("- Rectangle Detection: 检测结果窗口")
        print("- Debug Controls: 参数调节窗口")
        print("- Grayscale Image: 灰度图像（调试用）")
        print("- Binary Image: 二值化图像（调试用）")
        print("按键说明:")
        print("  q - 退出程序")
        print("  r - 重置参数")
        print("  s - 保存参数")
        print("  l - 重新加载参数")
        print("  h - 显示帮助")
        print("使用滑动条实时调节检测参数")
        print("观察二值化图像确保矩形为白色")
    
    def calculate_diagonal_intersection(self, rect_points):
        """计算矩形对角线交点"""
        if len(rect_points) != 4:
            return None
        
        points = rect_points.reshape(-1, 2)
        
        # 找到对角顶点
        sum_coords = points.sum(axis=1)
        top_left_idx = np.argmin(sum_coords)      # 左上角
        bottom_right_idx = np.argmax(sum_coords)  # 右下角
        
        diff_coords = points[:, 0] - points[:, 1]
        top_right_idx = np.argmin(diff_coords)    # 右上角
        bottom_left_idx = np.argmax(diff_coords)  # 左下角
        
        # 获取对角顶点坐标
        top_left = points[top_left_idx]
        bottom_right = points[bottom_right_idx]
        top_right = points[top_right_idx]
        bottom_left = points[bottom_left_idx]
        
        # 计算对角线交点
        x1, y1 = top_left
        x2, y2 = bottom_right
        x3, y3 = top_right
        x4, y4 = bottom_left
        
        # 线段交点公式
        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        if abs(denom) < 1e-10:
            # 平行线，返回几何中心
            center_x = (x1 + x2 + x3 + x4) / 4
            center_y = (y1 + y2 + y3 + y4) / 4
        else:
            t = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
            center_x = x1 + t*(x2-x1)
            center_y = y1 + t*(y2-y1)
        
        return (int(center_x), int(center_y))
    
    def detect_rectangles(self, image, min_area, max_area, threshold, epsilon_factor):
        """检测矩形 - 恢复think.py的完整逻辑"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 二值化
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)

        # 显示调试图像（恢复think.py的调试功能）
        cv2.imshow('Grayscale Image', gray)
        cv2.imshow('Binary Image', binary)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # 筛选矩形
        rectangles = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if min_area <= area <= max_area:
                # 打印面积信息（恢复调试信息）
                print(f"轮廓面积: {area}")

                # 多边形逼近
                epsilon = epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                if len(approx) == 4:  # 四边形
                    rectangles.append((approx, area))

        # 按面积排序（大到小）
        rectangles.sort(key=lambda x: x[1], reverse=True)
        return rectangles
    
    def process_frame(self, frame):
        """处理单帧图像"""
        # 获取调试参数
        min_area, max_area, threshold, epsilon_factor, scale_percent = get_debug_params()

        # 缩放图像
        width = int(frame.shape[1] * scale_percent / 100)
        height = int(frame.shape[0] * scale_percent / 100)
        resized = cv2.resize(frame, (width, height), interpolation=cv2.INTER_AREA)

        # 检测矩形
        rectangles = self.detect_rectangles(resized, min_area, max_area, threshold, epsilon_factor)
        
        # 绘制结果
        result = resized.copy()
        
        if len(rectangles) >= 2:
            # 双矩形情况：外框和内框 - 恢复think.py的完整逻辑
            outer_rect = rectangles[0][0]  # 外框（大）
            inner_rect = rectangles[1][0]  # 内框（小）

            # 绘制矩形轮廓
            cv2.drawContours(result, [outer_rect], -1, (0, 0, 255), 2)  # 红色外框
            cv2.drawContours(result, [inner_rect], -1, (0, 255, 0), 2)  # 绿色内框

            # 恢复think.py的顶点排序逻辑
            def sort_vertices(vertices):
                center_x = sum([p[0][0] for p in vertices]) / 4
                center_y = sum([p[0][1] for p in vertices]) / 4
                import math
                def angle_from_center(point):
                    return math.atan2(point[0][1] - center_y, point[0][0] - center_x)
                return sorted(vertices, key=angle_from_center)

            outer_sorted = sort_vertices(outer_rect)
            inner_sorted = sort_vertices(inner_rect)

            # 计算对应顶点的中点（恢复think.py逻辑）
            for i in range(4):
                outer_x, outer_y = outer_sorted[i][0]
                inner_x, inner_y = inner_sorted[i][0]

                mid_x = (outer_x + inner_x) // 2
                mid_y = (outer_y + inner_y) // 2

                # 绘制中点十字标记
                cross_size = 10
                cv2.line(result, (mid_x-cross_size, mid_y), (mid_x+cross_size, mid_y), (255, 255, 0), 2)
                cv2.line(result, (mid_x, mid_y-cross_size), (mid_x, mid_y+cross_size), (255, 255, 0), 2)

                cv2.putText(result, f"({mid_x},{mid_y})", (mid_x+15, mid_y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

            # 计算内部矩形对角线交点
            center_point = self.calculate_diagonal_intersection(inner_rect)

            if center_point:
                # 绘制交点
                cv2.circle(result, center_point, 8, (255, 0, 255), -1)  # 紫色圆点
                cv2.putText(result, f"Inner Center({center_point[0]},{center_point[1]})",
                           (center_point[0]+10, center_point[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)

                # 发送原始坐标到下位机
                self.serial_comm.send_red_laser_point(center_point)

                # 打印坐标
                print(f"内部矩形对角线交点: ({center_point[0]}, {center_point[1]})")
        
        elif len(rectangles) == 1:
            # 单矩形情况
            rect = rectangles[0][0]
            cv2.drawContours(result, [rect], -1, (0, 255, 0), 2)  # 绿色
            
            # 计算对角线交点
            center_point = self.calculate_diagonal_intersection(rect)
            
            if center_point:
                # 绘制交点
                cv2.circle(result, center_point, 8, (255, 0, 255), -1)
                cv2.putText(result, f"Center({center_point[0]},{center_point[1]})", 
                           (center_point[0]+10, center_point[1]-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)
                
                # 发送坐标
                self.serial_comm.send_red_laser_point(center_point)
                print(f"矩形对角线交点: ({center_point[0]}, {center_point[1]})")
        
        # 显示状态信息
        status_text = f"Rectangles: {len(rectangles)}"
        cv2.putText(result, status_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # 显示当前参数（简化版）
        param_text = f"Threshold: {threshold}, Scale: {scale_percent}%"
        cv2.putText(result, param_text, (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

        return result
    
    def run(self):
        """主运行循环"""
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("无法打开摄像头")
            return
        
        print("摄像头已启动，开始检测...")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("无法读取摄像头画面")
                break
            
            # 处理帧
            result = self.process_frame(frame)
            
            # 显示结果
            cv2.imshow('Rectangle Detection', result)
            
            # 按键检测
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('r'):  # 'r'键重置参数
                cv2.setTrackbarPos('Min Area', 'Debug Controls', 100)
                cv2.setTrackbarPos('Max Area', 'Debug Controls', 300)
                cv2.setTrackbarPos('Threshold', 'Debug Controls', 46)
                cv2.setTrackbarPos('Epsilon', 'Debug Controls', 3)
                cv2.setTrackbarPos('Scale', 'Debug Controls', 50)
                print("参数已重置为默认值")
            elif key == ord('s'):  # 's'键保存参数
                if save_params():
                    print("当前参数已保存，下次启动时会自动加载")
            elif key == ord('l'):  # 'l'键重新加载参数
                if load_params():
                    print("参数已重新加载")
            elif key == ord('h'):  # 'h'键显示帮助
                print("\n=== 帮助信息 ===")
                print("按键功能:")
                print("  q - 退出程序")
                print("  r - 重置所有参数")
                print("  s - 保存当前参数")
                print("  l - 重新加载参数")
                print("  h - 显示此帮助")
                print("调节滑动条来改变检测参数:")
                print("- Min Area: 最小轮廓面积")
                print("- Max Area: 最大轮廓面积 (实际值 = 滑动条值 × 100)")
                print("- Threshold: 二值化阈值")
                print("- Epsilon: 多边形逼近精度 (实际值 = 滑动条值 / 100)")
                print("- Scale: 图像缩放比例")
                print("参数会自动保存到 simple_rect_params.json")
                print("================\n")
        
        # 清理资源
        cap.release()
        cv2.destroyAllWindows()
        self.serial_comm.close()
        print("程序已退出")

def main():
    """主函数"""
    detector = SimpleRectDetector()
    detector.run()

if __name__ == "__main__":
    main()
