#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化矩形检测程序
功能：检测矩形，计算内部矩形对角线交点，发送原始坐标到下位机
"""

import cv2
import numpy as np
from serial_comm import SerialComm

class SimpleRectDetector:
    def __init__(self):
        # 检测参数
        self.min_area = 1000
        self.max_area = 30000
        self.threshold = 46
        self.epsilon_factor = 0.03
        
        # 串口通信
        self.serial_comm = SerialComm(port='/dev/ttyTHS0', baudrate=115200, enable=True)
        self.serial_comm.init()
        
        print("简化矩形检测程序启动")
        print("功能：检测双矩形，发送内部矩形对角线交点原始坐标")
        print("按 'q' 退出程序")
    
    def calculate_diagonal_intersection(self, rect_points):
        """计算矩形对角线交点"""
        if len(rect_points) != 4:
            return None
        
        points = rect_points.reshape(-1, 2)
        
        # 找到对角顶点
        sum_coords = points.sum(axis=1)
        top_left_idx = np.argmin(sum_coords)      # 左上角
        bottom_right_idx = np.argmax(sum_coords)  # 右下角
        
        diff_coords = points[:, 0] - points[:, 1]
        top_right_idx = np.argmin(diff_coords)    # 右上角
        bottom_left_idx = np.argmax(diff_coords)  # 左下角
        
        # 获取对角顶点坐标
        top_left = points[top_left_idx]
        bottom_right = points[bottom_right_idx]
        top_right = points[top_right_idx]
        bottom_left = points[bottom_left_idx]
        
        # 计算对角线交点
        x1, y1 = top_left
        x2, y2 = bottom_right
        x3, y3 = top_right
        x4, y4 = bottom_left
        
        # 线段交点公式
        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        if abs(denom) < 1e-10:
            # 平行线，返回几何中心
            center_x = (x1 + x2 + x3 + x4) / 4
            center_y = (y1 + y2 + y3 + y4) / 4
        else:
            t = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
            center_x = x1 + t*(x2-x1)
            center_y = y1 + t*(y2-y1)
        
        return (int(center_x), int(center_y))
    
    def detect_rectangles(self, image):
        """检测矩形"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 二值化
        _, binary = cv2.threshold(gray, self.threshold, 255, cv2.THRESH_BINARY)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        # 筛选矩形
        rectangles = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_area <= area <= self.max_area:
                # 多边形逼近
                epsilon = self.epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                if len(approx) == 4:  # 四边形
                    rectangles.append((approx, area))
        
        # 按面积排序（大到小）
        rectangles.sort(key=lambda x: x[1], reverse=True)
        return rectangles
    
    def process_frame(self, frame):
        """处理单帧图像"""
        # 缩放图像
        scale_percent = 50
        width = int(frame.shape[1] * scale_percent / 100)
        height = int(frame.shape[0] * scale_percent / 100)
        resized = cv2.resize(frame, (width, height), interpolation=cv2.INTER_AREA)
        
        # 检测矩形
        rectangles = self.detect_rectangles(resized)
        
        # 绘制结果
        result = resized.copy()
        
        if len(rectangles) >= 2:
            # 双矩形情况：外框和内框
            outer_rect = rectangles[0][0]  # 外框（大）
            inner_rect = rectangles[1][0]  # 内框（小）
            
            # 绘制矩形轮廓
            cv2.drawContours(result, [outer_rect], -1, (0, 0, 255), 2)  # 红色外框
            cv2.drawContours(result, [inner_rect], -1, (0, 255, 0), 2)  # 绿色内框
            
            # 计算内部矩形对角线交点
            center_point = self.calculate_diagonal_intersection(inner_rect)
            
            if center_point:
                # 绘制交点
                cv2.circle(result, center_point, 8, (255, 0, 255), -1)  # 紫色圆点
                cv2.putText(result, f"Center({center_point[0]},{center_point[1]})", 
                           (center_point[0]+10, center_point[1]-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)
                
                # 发送原始坐标到下位机
                self.serial_comm.send_red_laser_point(center_point)
                
                # 打印坐标
                print(f"内部矩形对角线交点: ({center_point[0]}, {center_point[1]})")
        
        elif len(rectangles) == 1:
            # 单矩形情况
            rect = rectangles[0][0]
            cv2.drawContours(result, [rect], -1, (0, 255, 0), 2)  # 绿色
            
            # 计算对角线交点
            center_point = self.calculate_diagonal_intersection(rect)
            
            if center_point:
                # 绘制交点
                cv2.circle(result, center_point, 8, (255, 0, 255), -1)
                cv2.putText(result, f"Center({center_point[0]},{center_point[1]})", 
                           (center_point[0]+10, center_point[1]-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)
                
                # 发送坐标
                self.serial_comm.send_red_laser_point(center_point)
                print(f"矩形对角线交点: ({center_point[0]}, {center_point[1]})")
        
        # 显示状态信息
        status_text = f"Rectangles: {len(rectangles)}"
        cv2.putText(result, status_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return result
    
    def run(self):
        """主运行循环"""
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("无法打开摄像头")
            return
        
        print("摄像头已启动，开始检测...")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("无法读取摄像头画面")
                break
            
            # 处理帧
            result = self.process_frame(frame)
            
            # 显示结果
            cv2.imshow('Rectangle Detection', result)
            
            # 按键检测
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
        
        # 清理资源
        cap.release()
        cv2.destroyAllWindows()
        self.serial_comm.close()
        print("程序已退出")

def main():
    """主函数"""
    detector = SimpleRectDetector()
    detector.run()

if __name__ == "__main__":
    main()
