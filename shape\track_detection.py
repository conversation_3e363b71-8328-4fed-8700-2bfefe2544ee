import cv2
import numpy as np
import os
import matplotlib
import matplotlib.pyplot as plt
from datetime import datetime
from scipy.interpolate import splprep, splev
import math
from collections import deque

# 设置支持中文的字体 - 解决中文显示问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class ROIObjectDetector:
    def __init__(self):
        # 黑色阈值参数 (HSV色彩空间更稳定)
        self.black_lower = np.array([0, 0, 0])
        self.black_upper = np.array([180, 255, 60])

        # 形态学操作核
        self.kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))

        # ROI矩形检测参数
        self.min_rect_area = 1000      # 最小矩形面积
        self.max_rect_area = 60000     # 最大矩形面积
        self.rect_aspect_ratio_min = 0.3  # 矩形最小宽高比
        self.rect_aspect_ratio_max = 3.0  # 矩形最大宽高比

        # 工作流程状态
        self.workflow_state = "detecting_roi"  # detecting_roi, detecting_objects
        self.selected_roi = None

    def detect_rectangles(self, img):
        """检测矩形ROI区域 - 改进版本，参考shape_unclose.py"""
        # 图像预处理 - 使用更强的预处理
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # 边缘检测 - 调整参数以更好地检测矩形
        edges = cv2.Canny(blurred, 50, 150)

        # 形态学操作 - 连接断开的边缘
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        rectangles = []

        # print(f"检测到 {len(contours)} 个轮廓")  # 调试信息

        for i, contour in enumerate(contours):
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            # print(f"轮廓 {i}: 面积 = {area}")  # 调试信息

            # 面积过滤
            if area < self.min_rect_area or area > self.max_rect_area:
                continue

            # 计算轮廓周长
            perimeter = cv2.arcLength(contour, True)

            # 多边形逼近 - 使用更精确的逼近
            epsilon = 0.02 * perimeter
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # print(f"轮廓 {i}: 顶点数 = {len(approx)}")  # 调试信息

            # 检查是否为四边形（矩形）
            if len(approx) == 4:
                # 计算边界矩形
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = float(w) / h

             #   print(f"矩形候选: 位置({x},{y}), 尺寸({w}x{h}), 宽高比={aspect_ratio:.2f}")  # 调试信息

                # 宽高比检查
                if self.rect_aspect_ratio_min <= aspect_ratio <= self.rect_aspect_ratio_max:
                    # 计算轮廓的紧密度（面积与边界矩形面积的比值）
                    rect_area = w * h
                    compactness = area / rect_area if rect_area > 0 else 0

                    # 只接受相对紧密的矩形（避免奇怪形状）
                    if compactness > 0.7:  # 至少70%的填充度
                        rectangles.append({
                            'contour': contour,
                            'approx': approx,
                            'bbox': (x, y, w, h),
                            'area': area,
                            'aspect_ratio': aspect_ratio,
                            'compactness': compactness
                        })
                       # print(f"✓ 矩形已添加: 面积={area:.0f}, 紧密度={compactness:.2f}")

        # 按面积排序，返回最大的矩形作为主ROI
        rectangles.sort(key=lambda x: x['area'], reverse=True)
       # print(f"最终检测到 {len(rectangles)} 个有效矩形")
        return rectangles

    def detect_black_blocks_in_roi(self, img, roi_bbox):
        """在ROI区域内检测黑色块并返回轮廓 - 使用灰度化+二值化+骨架提取优化"""
        x, y, w, h = roi_bbox

        # 提取ROI区域
        roi_img = img[y:y+h, x:x+w]

        # 转换为灰度图像
        gray = cv2.cvtColor(roi_img, cv2.COLOR_BGR2GRAY)

        # 使用OTSU自适应二值化 - 更高效且适应性更强
        # OTSU会自动找到最佳阈值，将图像分为白色背景和黑色线条/物体
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 形态学操作去噪 - 保持原有的去噪逻辑
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, self.kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, self.kernel)

        # 骨架提取 - 将粗线条变为单像素骨架线
        skeleton = self.extract_skeleton(binary)

        # 查找轮廓（在骨架化后的图像上）
        contours, _ = cv2.findContours(skeleton, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤面积太小的轮廓并转换为全局坐标
        valid_contours = []
        for contour in contours:
            if cv2.contourArea(contour) > 10:  # 骨架线面积很小，降低阈值
                # 转换为全局坐标
                contour_global = contour + np.array([x, y])
                valid_contours.append(contour_global)

        # 创建与原图相同尺寸的骨架图像
        full_skeleton = np.zeros(img.shape[:2], dtype=np.uint8)
        full_skeleton[y:y+h, x:x+w] = skeleton

        return valid_contours, binary, skeleton, full_skeleton

    def detect_black_blocks(self, img):
        """原始的全图检测黑色块函数（保持兼容性）"""
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

        # 创建黑色掩码
        mask = cv2.inRange(hsv, self.black_lower, self.black_upper)

        # 形态学操作去噪
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, self.kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, self.kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤面积太小的轮廓
        valid_contours = [c for c in contours if cv2.contourArea(c) > 500]

        return valid_contours, mask

    def extract_skeleton(self, binary_img):
        """提取骨架 - 使用高级Guo-Hall算法"""
        try:
            # 优先使用Guo-Hall算法（需要opencv-contrib-python）
            skeleton = cv2.ximgproc.thinning(binary_img, thinningType=cv2.ximgproc.THINNING_GUOHALL)
          #  print("使用Guo-Hall骨架化算法")
            return skeleton
        except AttributeError:
            # 如果没有ximgproc，使用传统方法
          #  print("使用传统骨架化算法")
            return self.traditional_skeleton(binary_img)

    def traditional_skeleton(self, binary_img):
        """传统骨架化算法作为备选"""
        kernel = cv2.getStructuringElement(cv2.MORPH_CROSS, (3, 3))
        skeleton = np.zeros(binary_img.shape, np.uint8)
        temp = binary_img.copy()

        while True:
            eroded = cv2.erode(temp, kernel)
            opened = cv2.morphologyEx(eroded, cv2.MORPH_OPEN, kernel)
            subset = cv2.subtract(eroded, opened)
            skeleton = cv2.bitwise_or(skeleton, subset)
            temp = eroded.copy()

            if cv2.countNonZero(temp) == 0:
                break

        return skeleton

    def extract_center_trajectory(self, skeleton):
        """从骨架中提取有序轨迹点 - 融合img_process.py算法"""
        # 查找连通组件，优先选择最大的
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(skeleton, connectivity=8)

        if num_labels <= 1:  # 只有背景，没有前景
          #  print("警告: 骨架中没有找到任何连通组件")
            return None

        # 找到最大的连通组件（排除背景标签0）
        largest_component_label = 1
        largest_area = stats[1, cv2.CC_STAT_AREA]

        for i in range(2, num_labels):
            area = stats[i, cv2.CC_STAT_AREA]
            if area > largest_area:
                largest_area = area
                largest_component_label = i

      #  print(f"找到 {num_labels-1} 个连通组件，选择最大的（标签{largest_component_label}，面积{largest_area}）")

        # 只提取最大连通组件的点
        largest_component_mask = (labels == largest_component_label).astype(np.uint8) * 255
        points = np.column_stack(np.where(largest_component_mask > 0))

        # 将坐标转换为(x, y)格式
        points = points[:, [1, 0]]  # 交换列顺序

        # 如果点集为空则返回
        if len(points) == 0:
          #  print("警告: 最大连通组件中没有找到任何点")
            return None

      #  print(f"从最大连通组件中提取到 {len(points)} 个点")

        # 使用拓扑排序获取有序轨迹点
        sorted_points = self.topological_sort_points(points)

      #  print(f"拓扑排序后得到 {len(sorted_points)} 个有序点")

        return sorted_points

    def topological_sort_points(self, points):
        """使用拓扑排序获取有序轨迹点 - 来自img_process.py"""
        # 将点转换为元组集合以便快速查找
        point_set = set(tuple(p) for p in points)

        # 如果没有点，返回空列表
        if not point_set:
            return []

      #  print(f"开始拓扑排序，点集大小: {len(point_set)}")

        # 找到端点（只有一个邻居的点）
        endpoints = []
        for p in point_set:
            neighbors = self.get_8_neighbors(p, point_set)
            if len(neighbors) == 1:
                endpoints.append(p)

       # print(f"找到 {len(endpoints)} 个端点")

        # 如果没有端点（闭环），则使用x坐标最小的点
        if not endpoints:
            start_point = min(point_set, key=lambda p: p[0])
         #   print(f"没有端点，使用最左点作为起点: {start_point}")
        else:
            # 使用x坐标最小的端点作为起点
            start_point = min(endpoints, key=lambda p: p[0])
           # print(f"使用最左端点作为起点: {start_point}")

        # 使用BFS遍历点
        visited = set()
        queue = deque([start_point])
        sorted_points = []

        while queue:
            current = queue.popleft()
            if current in visited:
                continue

            visited.add(current)
            sorted_points.append(current)

            # 获取当前点的邻居
            neighbors = self.get_8_neighbors(current, point_set)

            # 按方向连续性排序邻居（优先选择与上一方向一致的邻居）
            if len(sorted_points) > 1:
                prev_point = sorted_points[-2]
                dx = current[0] - prev_point[0]
                dy = current[1] - prev_point[1]

                # 优先选择方向一致的邻居
                neighbors.sort(key=lambda n: abs((n[0] - current[0]) - dx) + abs((n[1] - current[1]) - dy))

            # 添加未访问的邻居
            for neighbor in neighbors:
                if neighbor not in visited:
                    queue.append(neighbor)

        #print(f"BFS遍历完成，访问了 {len(sorted_points)} 个点")

        # 转换为numpy数组
        return np.array([list(p) for p in sorted_points])

    def get_8_neighbors(self, point, point_set):
        """获取8邻域内的邻居点 - 来自img_process.py"""
        x, y = point
        neighbors = []

        # 检查8个方向
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                if dx == 0 and dy == 0:
                    continue

                neighbor = (x + dx, y + dy)
                if neighbor in point_set:
                    neighbors.append(neighbor)

        return neighbors

    def interpolate_trajectory(self, points, num_points=100):
        """对轨迹点进行样条插值 - 来自img_process.py"""
        if points is None or len(points) < 4:
           #print("点数不足，跳过插值")
            return points

        try:
            # 添加0.1%的随机抖动避免重复x值
            jitter = np.random.normal(0, 0.001, points.shape)
            points = points.astype(float) + jitter

            # 样条插值
            tck, u = splprep(points.T, u=None, s=0.0, per=0)
            u_new = np.linspace(u.min(), u.max(), num_points)
            x_new, y_new = splev(u_new, tck, der=0)

            interpolated = np.column_stack((x_new, y_new))
            #print(f"样条插值完成: {len(points)} -> {len(interpolated)} 点")

            return interpolated
        except Exception as e:
           # print(f"插值失败: {e}, 返回原始点")
            return points

    def save_trajectory_data(self, trajectory_points, interpolated_points):
        """保存轨迹数据到文件"""
        try:
            # 创建保存目录
            save_dir = "trajectory_results"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存原始轨迹点
            if trajectory_points is not None:
                trajectory_path = os.path.join(save_dir, f"trajectory_raw_{timestamp}.npy")
                np.save(trajectory_path, trajectory_points)
               # print(f"原始轨迹点已保存: {trajectory_path}")

            # 保存插值轨迹点
            if interpolated_points is not None:
                interpolated_path = os.path.join(save_dir, f"trajectory_smooth_{timestamp}.npy")
                np.save(interpolated_path, interpolated_points)
               # print(f"平滑轨迹点已保存: {interpolated_path}")

        except Exception as e:
            #print(f"保存轨迹数据失败: {e}")
            pass

    def visualize_trajectory_matplotlib(self, img, trajectory_points, interpolated_points):
        """使用matplotlib可视化轨迹"""
        try:
            plt.figure(figsize=(12, 8))

            # 将OpenCV的BGR图像转换为RGB格式显示
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            plt.imshow(img_rgb)

            # 绘制原始轨迹点（按顺序）
            if trajectory_points is not None and len(trajectory_points) > 1:
                for i in range(1, len(trajectory_points)):
                    plt.plot([trajectory_points[i - 1, 0], trajectory_points[i, 0]],
                             [trajectory_points[i - 1, 1], trajectory_points[i, 1]],
                             'r-', linewidth=1, alpha=0.7, label='原始轨迹' if i == 1 else "")

            # 绘制插值后轨迹
            if interpolated_points is not None and len(interpolated_points) > 1:
                plt.plot(interpolated_points[:, 0], interpolated_points[:, 1],
                         'b-', linewidth=2, label='平滑轨迹')

            plt.legend()
            plt.title('实时轨迹检测结果')
            plt.axis('off')

            # 非阻塞显示
            plt.draw()
            plt.pause(0.001)

        except Exception as e:
            print(f"matplotlib可视化失败: {e}")
            pass

    def extract_center_points(self, contours):
        """提取轮廓中心点"""
        centers = []
        for contour in contours:
            # 计算轮廓的矩
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                centers.append((cx, cy))
        return centers

    def process_frame(self, img):
        """处理流程：先检测ROI，再检测黑色物体"""
        if self.workflow_state == "detecting_roi":
            # 步骤1: 检测矩形ROI
            rectangles = self.detect_rectangles(img)

            if len(rectangles) > 0:
                # 自动选择最大的矩形作为ROI
                self.selected_roi = rectangles[0]
                self.workflow_state = "detecting_objects"
               # print(f"自动选择ROI: {self.selected_roi['bbox']}, 面积: {self.selected_roi['area']:.0f}")
                return "roi_detected", rectangles, None
            else:
                return "no_roi", [], None

        elif self.workflow_state == "detecting_objects":
            # 步骤2: 在ROI内检测黑色物体
            if self.selected_roi is None:
                self.workflow_state = "detecting_roi"
                return "roi_lost", [], None

            # 在ROI内检测黑色块并提取轨迹
            contours, binary_mask, skeleton, full_skeleton = self.detect_black_blocks_in_roi(img, self.selected_roi['bbox'])

            # 提取有序轨迹点
            trajectory_points = self.extract_center_trajectory(skeleton)

            # 转换轨迹点到全局坐标系
            global_trajectory = None
            interpolated_trajectory = None

            if trajectory_points is not None and len(trajectory_points) > 0:
                # 转换到全局坐标
                x, y, w, h = self.selected_roi['bbox']
                global_trajectory = trajectory_points.copy()
                global_trajectory[:, 0] += x  # 加上ROI的x偏移
                global_trajectory[:, 1] += y  # 加上ROI的y偏移

                #print(f"轨迹点已转换到全局坐标系，共 {len(global_trajectory)} 个点")

                # 样条插值平滑轨迹
                interpolated_trajectory = self.interpolate_trajectory(global_trajectory, 200)

                # 保存轨迹数据（可选）
                self.save_trajectory_data(global_trajectory, interpolated_trajectory)

            # 提取传统中心点（保持兼容性）
            centers = self.extract_center_points(contours)

            return "objects_detected", [self.selected_roi], {
                'centers': centers,
                'contours': contours,
                'mask': binary_mask,
                'skeleton': full_skeleton,
                'trajectory_points': global_trajectory,
                'interpolated_trajectory': interpolated_trajectory,
                'count': len(centers)
            }
    
    def visualize_results(self, img, status, rectangles, detection_data=None):
        """在原始图像上可视化检测结果"""
        result_img = img.copy()

        # 绘制ROI矩形
        if rectangles:
            for i, rect in enumerate(rectangles):
                if 'bbox' in rect:
                    x, y, w, h = rect['bbox']
                    color = (0, 255, 0) if self.selected_roi == rect else (255, 255, 0)
                    # 绘制ROI矩形框（加粗显示）
                    cv2.rectangle(result_img, (x, y), (x+w, y+h), color, 3)
                    cv2.putText(result_img, f"ROI {i}", (x, y-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                    cv2.putText(result_img, f"Area: {rect['area']:.0f}", (x, y+h+25),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        # 绘制轨迹点
        if detection_data and isinstance(detection_data, dict):
            # 绘制原始轨迹点
            trajectory_points = detection_data.get('trajectory_points', None)
            if trajectory_points is not None and len(trajectory_points) > 1:
                # 绘制轨迹线
                for i in range(1, len(trajectory_points)):
                    pt1 = tuple(map(int, trajectory_points[i-1]))
                    pt2 = tuple(map(int, trajectory_points[i]))
                    cv2.line(result_img, pt1, pt2, (0, 255, 0), 2)  # 绿色轨迹线

                # 标记起点和终点
                start_point = tuple(map(int, trajectory_points[0]))
                end_point = tuple(map(int, trajectory_points[-1]))
                cv2.circle(result_img, start_point, 8, (255, 0, 0), -1)  # 蓝色起点
                cv2.circle(result_img, end_point, 8, (0, 0, 255), -1)    # 红色终点

                # 添加标签
                cv2.putText(result_img, "START", (start_point[0]-20, start_point[1]-15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
                cv2.putText(result_img, "END", (end_point[0]+10, end_point[1]-15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

                # 显示轨迹点数量
                cv2.putText(result_img, f"Trajectory Points: {len(trajectory_points)}", (10, 90),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 绘制插值轨迹（可选）
            interpolated_points = detection_data.get('interpolated_trajectory', None)
            if interpolated_points is not None and len(interpolated_points) > 1:
                # 绘制平滑轨迹（半透明）
                overlay = result_img.copy()
                for i in range(1, len(interpolated_points)):
                    pt1 = tuple(map(int, interpolated_points[i-1]))
                    pt2 = tuple(map(int, interpolated_points[i]))
                    cv2.line(overlay, pt1, pt2, (255, 255, 0), 1)  # 黄色平滑轨迹

                # 混合显示
                cv2.addWeighted(overlay, 0.3, result_img, 0.7, 0, result_img)

            # 绘制传统中心点（保持兼容性）
            centers = detection_data.get('centers', [])
            for i, center in enumerate(centers):
                cx, cy = center
                # 绘制中心点（小圆圈）
                cv2.circle(result_img, (cx, cy), 4, (255, 0, 255), -1)  # 紫色中心点

        # 显示状态信息
        status_text = {
            "detecting_roi": "Step 1: Detecting Rectangle ROI",
            "roi_detected": "Step 1: ROI Detected",
            "no_roi": "Step 1: No ROI Found",
            "objects_detected": "Step 2: Black Objects Detected",
            "roi_lost": "ROI Lost - Redetecting"
        }

        cv2.putText(result_img, status_text.get(status, status), (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # 显示检测到的物体数量
        if detection_data and isinstance(detection_data, dict):
            count = detection_data.get('count', 0)
            cv2.putText(result_img, f"Objects: {count}", (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return result_img

# 使用示例
def main():
    detector = ROIObjectDetector()
    cap = cv2.VideoCapture(0)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    print("=== 智能轨迹导航检测系统 ===")
    print("功能特性:")
    print("1. 自动检测矩形ROI区域")
    print("2. 高级骨架化轨迹提取")
    print("3. 8邻域拓扑排序算法")
    print("4. 样条插值轨迹平滑")
    print("5. 实时轨迹可视化")
    print("6. 自动数据保存")
    print("按键控制:")
    print("  'q' - 退出程序")
    print("  's' - 保存当前轨迹")
    print("  'm' - 切换matplotlib显示")
    print("  'r' - 重置ROI区域")
    print("=" * 50)

    frame_count = 0
    show_matplotlib = False
    plt_figure = None

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        frame_count += 1

        # 处理帧
        status, rectangles, detection_data = detector.process_frame(frame)

        # 直接在原始图像上进行可视化标注
        annotated_frame = detector.visualize_results(frame, status, rectangles, detection_data)

        # 只显示标注后的原始图像
        cv2.imshow('Original', annotated_frame)

        # 显示掩码（如果有检测数据）
        if isinstance(detection_data, dict) and 'mask' in detection_data:
            cv2.imshow('Mask', detection_data['mask'])

        # 显示骨架（如果有检测数据）
        if isinstance(detection_data, dict) and 'skeleton' in detection_data:
            cv2.imshow('Skeleton', detection_data['skeleton'])

        # 打印详细检测信息
        if status == "objects_detected" and isinstance(detection_data, dict):
            trajectory_points = detection_data.get('trajectory_points', None)
            interpolated_points = detection_data.get('interpolated_trajectory', None)
            centers = detection_data.get('centers', [])

            print(f"\n=== Frame {frame_count} 检测结果 ===")

            if trajectory_points is not None:
                print(f"✓ 轨迹点数量: {len(trajectory_points)}")
                if len(trajectory_points) > 0:
                    start_point = trajectory_points[0]
                    end_point = trajectory_points[-1]
                    print(f"  起点: ({start_point[0]:.1f}, {start_point[1]:.1f})")
                    print(f"  终点: ({end_point[0]:.1f}, {end_point[1]:.1f})")

                    # 计算轨迹长度
                    total_length = 0
                    for i in range(1, len(trajectory_points)):
                        dx = trajectory_points[i][0] - trajectory_points[i-1][0]
                        dy = trajectory_points[i][1] - trajectory_points[i-1][1]
                        total_length += math.sqrt(dx*dx + dy*dy)
                    print(f"  轨迹总长度: {total_length:.1f} 像素")

            if interpolated_points is not None:
                print(f"✓ 平滑轨迹点数量: {len(interpolated_points)}")

            if centers:
                print(f"✓ 传统中心点数量: {len(centers)}")

            # matplotlib可视化（可选）
            if show_matplotlib and trajectory_points is not None:
                detector.visualize_trajectory_matplotlib(frame, trajectory_points, interpolated_points)

        elif status == "roi_detected":
            print(f"Frame {frame_count}: ✓ ROI检测成功")
        elif status == "no_roi":
            if frame_count % 30 == 0:  # 每30帧打印一次，避免刷屏
                print(f"Frame {frame_count}: ⚠ 未检测到ROI")

        # 键盘控制
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            print("退出程序...")
            break
        elif key == ord('s') and isinstance(detection_data, dict):
            # 手动保存当前轨迹
            trajectory_points = detection_data.get('trajectory_points', None)
            interpolated_points = detection_data.get('interpolated_trajectory', None)
            if trajectory_points is not None:
                detector.save_trajectory_data(trajectory_points, interpolated_points)
                print("✓ 手动保存轨迹数据完成")
        elif key == ord('m'):
            # 切换matplotlib显示
            show_matplotlib = not show_matplotlib
            print(f"matplotlib显示: {'开启' if show_matplotlib else '关闭'}")
            if not show_matplotlib:
                plt.close('all')
        elif key == ord('r'):
            # 重置ROI区域
            detector.workflow_state = "detecting_roi"
            detector.selected_roi = None
            print("✓ ROI区域已重置，重新开始检测矩形")

    # 程序结束清理
    print("\n=== 程序结束清理 ===")
    cap.release()
    cv2.destroyAllWindows()
    plt.close('all')  # 关闭所有matplotlib窗口
    print("✓ 资源清理完成")

if __name__ == "__main__":
    main()