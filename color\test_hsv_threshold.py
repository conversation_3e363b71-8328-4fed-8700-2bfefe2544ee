#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSV阈值读取功能测试脚本
用于验证hsv_processor.py中的HSV阈值读取功能是否正常工作
"""

import os
import sys

# 添加当前目录到路径，以便导入hsv_processor模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_hsv_threshold_reading():
    """测试HSV阈值读取功能"""
    print("=" * 60)
    print("HSV阈值读取功能测试")
    print("=" * 60)
    
    # 检查阈值文件是否存在
    threshold_file = "thresholds_HSV.txt"
    print(f"1. 检查阈值文件: {threshold_file}")
    
    if os.path.exists(threshold_file):
        print("   ✓ 阈值文件存在")
        
        # 读取并显示文件内容
        print("\n2. 阈值文件内容:")
        try:
            with open(threshold_file, "r") as f:
                lines = f.readlines()
                for i, line in enumerate(lines, 1):
                    print(f"   第{i}行: {line.strip()}")
        except Exception as e:
            print(f"   ✗ 读取文件失败: {e}")
            return False
    else:
        print("   ✗ 阈值文件不存在")
        return False
    
    # 测试参数解析
    print("\n3. 测试参数解析:")
    try:
        with open(threshold_file, "r") as f:
            lines = f.readlines()
            
        # 解析HSV下限值 (第1行)
        if len(lines) >= 1:
            hsv_lower = [int(x) for x in lines[0].strip().split(",")]
            print(f"   HSV下限值: H={hsv_lower[0]}, S={hsv_lower[1]}, V={hsv_lower[2]}")
        else:
            print("   ✗ 缺少HSV下限值")
            return False
            
        # 解析HSV上限值 (第2行)
        if len(lines) >= 2:
            hsv_upper = [int(x) for x in lines[1].strip().split(",")]
            print(f"   HSV上限值: H={hsv_upper[0]}, S={hsv_upper[1]}, V={hsv_upper[2]}")
        else:
            print("   ✗ 缺少HSV上限值")
            return False
            
        # 解析ROI区域 (第3行)
        if len(lines) >= 3:
            roi = [int(x) for x in lines[2].strip().split(",")]
            print(f"   ROI区域: x1={roi[0]}, y1={roi[1]}, x2={roi[2]}, y2={roi[3]}")
        else:
            print("   ✗ 缺少ROI区域")
            return False
            
        # 解析曝光值 (第4行，可选)
        if len(lines) >= 4:
            exposure = int(lines[3].strip())
            print(f"   曝光值: {exposure}")
        else:
            print("   曝光值: 未设置")
            
        print("   ✓ 参数解析成功")
        
    except Exception as e:
        print(f"   ✗ 参数解析失败: {e}")
        return False
    
    # 测试hsv_processor模块的加载功能
    print("\n4. 测试hsv_processor模块加载:")
    try:
        # 导入hsv_processor模块
        import hsv_processor
        
        # 保存原始参数
        original_h_min = hsv_processor.H_MIN
        original_h_max = hsv_processor.H_MAX
        original_s_min = hsv_processor.S_MIN
        original_s_max = hsv_processor.S_MAX
        original_v_min = hsv_processor.V_MIN
        original_v_max = hsv_processor.V_MAX
        
        print(f"   加载前参数: H({original_h_min}-{original_h_max}), S({original_s_min}-{original_s_max}), V({original_v_min}-{original_v_max})")
        
        # 调用加载函数
        hsv_processor.load_parameters()
        
        # 检查参数是否更新
        print(f"   加载后参数: H({hsv_processor.H_MIN}-{hsv_processor.H_MAX}), S({hsv_processor.S_MIN}-{hsv_processor.S_MAX}), V({hsv_processor.V_MIN}-{hsv_processor.V_MAX})")
        
        # 验证参数是否与文件内容一致
        if (hsv_processor.H_MIN == hsv_lower[0] and hsv_processor.S_MIN == hsv_lower[1] and 
            hsv_processor.V_MIN == hsv_lower[2] and hsv_processor.H_MAX == hsv_upper[0] and 
            hsv_processor.S_MAX == hsv_upper[1] and hsv_processor.V_MAX == hsv_upper[2]):
            print("   ✓ HSV参数加载正确")
        else:
            print("   ✗ HSV参数加载不正确")
            return False
            
        # 检查ROI参数
        if hasattr(hsv_processor, 'ROI') and hsv_processor.ROI == roi:
            print("   ✓ ROI参数加载正确")
        else:
            print("   ✗ ROI参数加载不正确")
            
        print("   ✓ hsv_processor模块加载功能正常")
        
    except ImportError as e:
        print(f"   ✗ 无法导入hsv_processor模块: {e}")
        return False
    except Exception as e:
        print(f"   ✗ hsv_processor模块测试失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✓ HSV阈值读取功能测试通过！")
    print("=" * 60)
    return True

def test_hsv_threshold_validation():
    """验证HSV阈值的有效性"""
    print("\n5. HSV阈值有效性验证:")
    
    try:
        import hsv_processor
        
        # 检查HSV值范围
        h_min, h_max = hsv_processor.H_MIN, hsv_processor.H_MAX
        s_min, s_max = hsv_processor.S_MIN, hsv_processor.S_MAX
        v_min, v_max = hsv_processor.V_MIN, hsv_processor.V_MAX
        
        # H值范围检查 (0-179)
        if 0 <= h_min <= 179 and 0 <= h_max <= 179 and h_min <= h_max:
            print(f"   ✓ H值范围有效: {h_min}-{h_max}")
        else:
            print(f"   ✗ H值范围无效: {h_min}-{h_max} (应在0-179之间)")
            
        # S值范围检查 (0-255)
        if 0 <= s_min <= 255 and 0 <= s_max <= 255 and s_min <= s_max:
            print(f"   ✓ S值范围有效: {s_min}-{s_max}")
        else:
            print(f"   ✗ S值范围无效: {s_min}-{s_max} (应在0-255之间)")
            
        # V值范围检查 (0-255)
        if 0 <= v_min <= 255 and 0 <= v_max <= 255 and v_min <= v_max:
            print(f"   ✓ V值范围有效: {v_min}-{v_max}")
        else:
            print(f"   ✗ V值范围无效: {v_min}-{v_max} (应在0-255之间)")
            
        # 检查颜色范围是否合理（蓝紫色）
        if 100 <= h_min <= 160 and 100 <= h_max <= 160:
            print("   ✓ 颜色范围符合蓝紫色特征")
        else:
            print(f"   ⚠ 颜色范围可能不是蓝紫色: H({h_min}-{h_max})")
            
    except Exception as e:
        print(f"   ✗ HSV阈值验证失败: {e}")

if __name__ == "__main__":
    # 切换到脚本所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 运行测试
    success = test_hsv_threshold_reading()
    
    if success:
        test_hsv_threshold_validation()
    
    print("\n测试完成！")
