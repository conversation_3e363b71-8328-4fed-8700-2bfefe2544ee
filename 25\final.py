
import cv2
import numpy as np
import os
from serial_comm import SerialComm  # 导入串口通信类

# 全局参数（初始化为默认值）
GRAY_MIN = 0
GRAY_MAX = 255
BRIGHTNESS = 0  
CONTRAST = 100  
ROI = [796, 233, 1093, 423]
INVERT = False

USE_THRESHOLD_FILE = True
DILATE_KERNEL_SIZE = 4
DILATE_ITERATIONS = 4

serial_comm = SerialComm(port='/dev/ttyTHS0', baudrate=115200, enable=True)

# 屏幕显示调试开关
SHOW_WINDOWS = False

def load_parameters():
    global GRAY_MIN, GRAY_MAX, BRIGHTNESS, CONTRAST, ROI
    if not USE_THRESHOLD_FILE:
        print("不读取阈值文件，使用全局参数")
        return
    try:
        if os.path.exists("thresholds_灰度.txt"):
            with open("thresholds_灰度.txt", "r") as f:
                lines = f.readlines()
                GRAY_MIN = int(lines[0].strip())
                GRAY_MAX = int(lines[1].strip())
                ROI = [int(x) for x in lines[2].strip().split(",")]
                if len(lines) > 3:
                    BRIGHTNESS = int(lines[3].strip())
                if len(lines) > 4:
                    CONTRAST = int(lines[4].strip())
                print(f"已加载灰度参数: ({GRAY_MIN}-{GRAY_MAX}) ROI:{ROI} 亮度:{BRIGHTNESS} 对比度:{CONTRAST/100.0:.1f}")
        else:
            print("阈值文件不存在，使用默认参数")
    except Exception as e:
        print(f"加载参数时出错: {str(e)}")

def save_parameters():
    global GRAY_MIN, GRAY_MAX, ROI, BRIGHTNESS, CONTRAST
    with open("thresholds_灰度.txt", "w") as f:
        f.write(f"{GRAY_MIN}\n")
        f.write(f"{GRAY_MAX}\n")
        f.write(",".join(map(str, ROI)) + "\n")
        f.write(f"{BRIGHTNESS}\n")
        f.write(f"{CONTRAST}\n")
    print("灰度阈值、亮度、对比度已保存到 thresholds_灰度.txt")

def adjust_brightness_contrast(image, brightness=0, contrast=1.0):
    return cv2.convertScaleAbs(image, alpha=contrast, beta=brightness)

def analyze_laser_color(image, contour):
    mask = np.zeros(image.shape[:2], dtype=np.uint8)
    cv2.drawContours(mask, [contour], 0, 255, -1)
    masked_image = cv2.bitwise_and(image, image, mask=mask)
    non_zero_pixels = cv2.countNonZero(mask)
    if non_zero_pixels == 0:
        return "UNKNOWN"
    b_sum = int(np.sum(masked_image[:, :, 0]))
    g_sum = int(np.sum(masked_image[:, :, 1]))
    r_sum = int(np.sum(masked_image[:, :, 2]))
    if g_sum > r_sum - 3000:
        return "GREEN"
    if r_sum > g_sum:
        return "RED"
    return "UNKNOWN"

def process_frame(frame):
    global serial_comm, SHOW_WINDOWS, GRAY_MIN, GRAY_MAX
    if frame is None or frame.size == 0:
        return None, None, None, None, None
    frame = frame.copy()
    orig_frame = frame.copy()
    frame = adjust_brightness_contrast(frame, BRIGHTNESS, CONTRAST / 100.0)

    roi_frame = None
    binary_result = contour_result = edge_result = rectangle_result = None

    if ROI:
        x1, y1, x2, y2 = ROI
        h, w = frame.shape[:2]
        x1, y1, x2, y2 = max(0, x1), max(0, y1), min(x2, w-1), min(y2, h-1)
        if x2 > x1 and y2 > y1:
            roi_frame = orig_frame[y1:y2, x1:x2]
            if SHOW_WINDOWS:
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            if roi_frame.size > 0:
                gray = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2GRAY)
                binary = cv2.inRange(gray, GRAY_MIN, GRAY_MAX)
                if INVERT:
                    binary = cv2.bitwise_not(binary)
                kernel = np.ones((DILATE_KERNEL_SIZE, DILATE_KERNEL_SIZE), np.uint8)
                binary = cv2.dilate(binary, kernel, iterations=DILATE_ITERATIONS)
                if SHOW_WINDOWS:
                    binary_result = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
                    edge_result = cv2.cvtColor(cv2.Canny(binary, 50, 150), cv2.COLOR_GRAY2BGR)
                    contour_result = roi_frame.copy()
                    rectangle_result = roi_frame.copy()
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if SHOW_WINDOWS:
                    cv2.drawContours(contour_result, contours, -1, (0, 255, 255), 2)
                for contour in contours:
                    if 5 < cv2.contourArea(contour) < 500:
                        x, y, w_box, h_box = cv2.boundingRect(contour)
                        laser_type = analyze_laser_color(roi_frame, contour)
                        center_x, center_y = x + w_box // 2, y + h_box // 2
                        global_x, global_y = ROI[0] + center_x, ROI[1] + center_y
                        serial_comm.send_red_laser_point((global_x, global_y))
                        if SHOW_WINDOWS:
                            rect_color = (0, 0, 255) if laser_type == "RED" else (0, 255, 0) if laser_type == "GREEN" else (0, 255, 255)
                            cv2.rectangle(rectangle_result, (x, y), (x+w_box, y+h_box), rect_color, 2)
                            cv2.drawMarker(rectangle_result, (center_x, center_y), (0, 0, 255), cv2.MARKER_CROSS, 10, 2)
    return frame, binary_result, edge_result, contour_result, rectangle_result

def on_gray_min(val): 
    global GRAY_MIN; GRAY_MIN = val
def on_gray_max(val): 
    global GRAY_MAX; GRAY_MAX = val
def on_brightness(val):
    global BRIGHTNESS; BRIGHTNESS = val
def on_contrast(val):
    global CONTRAST; CONTRAST = val

def main():
    global SHOW_WINDOWS
    load_parameters()
    serial_comm.init()
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("无法打开摄像头")
        return

    if SHOW_WINDOWS:
        cv2.namedWindow("Controls")
        cv2.createTrackbar("Gray Min", "Controls", GRAY_MIN, 255, on_gray_min)
        cv2.createTrackbar("Gray Max", "Controls", GRAY_MAX, 255, on_gray_max)
        cv2.createTrackbar("Brightness", "Controls", BRIGHTNESS, 100, on_brightness)
        cv2.createTrackbar("Contrast", "Controls", CONTRAST, 300, on_contrast)

    while True:
        ret, frame = cap.read()
        if not ret: break
        original, binary, edges, contours, rectangles = process_frame(frame)

        if SHOW_WINDOWS:
            cv2.imshow("Original", original)
            if binary is not None: cv2.imshow("Binary", binary)
            if edges is not None: cv2.imshow("Edges", edges)
            if contours is not None: cv2.imshow("Contours", contours)
            if rectangles is not None: cv2.imshow("Rectangles", rectangles)

        key = cv2.waitKey(30) & 0xFF
        if key == 27:  # ESC退出
            save_parameters()
            break

    cap.release()
    serial_comm.close()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
