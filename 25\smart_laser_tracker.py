#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能激光轨迹跟踪器
功能：
1. 读取111程序导出的轨迹坐标文件（目标路径）
2. 实时检测红色激光点当前位置（使用gray_processor功能）
3. 根据当前位置和目标轨迹进行路径规划
4. 30ms周期发送控制命令给下位机
"""

import cv2
import numpy as np
import time
import json
import os
import glob
import serial
import struct

class SerialComm:
    """串口通信类 - 集成自serial_comm.py"""
    
    def __init__(self, port='/dev/ttyTHS0', baudrate=115200, enable=True):
        self.port = port
        self.baudrate = baudrate
        self.enable = enable
        self.connection = None
        self.last_rect_send_time = 0
        self.rect_send_interval = 0.5  # 矩形发送间隔500ms
        
        # 指令类型定义
        self.CMD_RECT_VERTICES = 0x03  # 方框顶点坐标
        self.CMD_LASER_POINT = 0x03    # 激光点坐标
        self.CMD_TARGET_POSITION = 0x03  # 目标位置坐标（智能跟踪器专用）
        self.CMD_LASER_AND_TARGET = 0x03  # 当前激光点+目标点（8字节数据）
        
    def init(self):
        """初始化串口连接"""
        if not self.enable:
            print("串口发送功能已禁用")
            return None
            
        try:
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"串口 {self.port} 已打开，波特率 {self.baudrate}")
            return self.connection
        except serial.SerialException as e:
            print(f"无法打开串口 {self.port}: {e}")
            self.connection = None
            return None
    
    def create_packet(self, cmd, data):
        """创建串口通信数据包"""
        header = bytes([0xAA, 0x55])  # 帧头
        cmd_byte = bytes([cmd])       # 指令类型
        data_len = len(data)
        len_byte = bytes([data_len])  # 数据长度
        
        # 校验和计算
        checksum = cmd + data_len
        for byte in data:
            checksum += byte
        checksum = checksum & 0xFF
        checksum_byte = bytes([checksum])
        
        return header + cmd_byte + len_byte + data + checksum_byte
    
    def send_target_position(self, point):
        """发送目标位置坐标（30ms周期）- 使用03数据帧"""
        if self.connection is None or not self.enable or point is None:
            return False

        try:
            x = max(0, int(point[0]))
            y = max(0, int(point[1]))
            data = struct.pack('<HH', x, y)

            packet = self.create_packet(self.CMD_TARGET_POSITION, data)  # 使用03数据帧
            self.connection.write(packet)
            return True

        except Exception as e:
            print(f"发送目标位置数据时出错: {e}")
            return False

    def send_laser_and_target(self, current_laser, target_point, frame_time=None):
        """发送当前激光点和目标点坐标（30ms周期）- 使用04数据帧"""
        if self.connection is None or not self.enable:
            return False

        try:
            # 当前激光点坐标（如果没有检测到则使用0,0）
            if current_laser is not None:
                laser_x = max(0, int(current_laser[0]))
                laser_y = max(0, int(current_laser[1]))
            else:
                laser_x = 0
                laser_y = 0

            # 目标点坐标
            if target_point is not None:
                target_x = max(0, int(target_point[0]))
                target_y = max(0, int(target_point[1]))
            else:
                target_x = 0
                target_y = 0

            # 打包数据：当前激光点(4字节) + 目标点(4字节) = 8字节
            data = struct.pack('<HHHH', laser_x, laser_y, target_x, target_y)

            packet = self.create_packet(self.CMD_LASER_AND_TARGET, data)  # 使用04数据帧
            self.connection.write(packet)

            # 同步打印到控制台
            frame_time_str = f" | 帧时间: {frame_time:.1f}ms" if frame_time is not None else ""
            print(f"📡 串口发送: 当前({laser_x}, {laser_y}) -> 目标({target_x}, {target_y}){frame_time_str}")

            return True

        except Exception as e:
            print(f"发送激光点和目标点数据时出错: {e}")
            return False
    
    def close(self):
        """关闭串口连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            print("串口已关闭")

class SmartLaserTracker:
    def __init__(self):
        # 轨迹数据
        self.target_trajectory = []  # 目标轨迹点
        self.current_target_index = 0
        self.total_points = 0

        # 激光检测参数（来自gray_processor）
        self.gray_min = 226
        self.gray_max = 255
        self.brightness = 0
        self.contrast = 100
        self.roi = [796, 233, 1093, 423]  # x1, y1, x2, y2
        self.invert = False  # 添加INVERT参数
        self.dilate_kernel_size = 4
        self.dilate_iterations = 4

        # 控制参数
        self.distance_threshold = 5.0  # 距离阈值（像素）
        self.max_time_per_point = 300  # 最大停留时间（ms）
        self.min_time_per_point = 100  # 最小停留时间（ms）
        self.control_cycle = 30        # 控制周期（ms）

        # 坐标偏移参数
        self.y_offset = 170  # Y轴偏移量

        # 状态变量
        self.current_laser_position = None  # 当前检测到的激光位置
        self.target_position = None         # 当前目标位置
        self.start_position = None          # 起始点位置
        self.is_tracking = False
        self.tracking_started = False       # 是否已开始循迹
        self.start_distance_threshold = 5.0 # 距离起始点的阈值
        self.start_time = 0
        self.last_switch_time = 0
        self.last_detection_time = 0

        # 优化相关变量
        self.print_counter = 0              # 打印计数器
        self.print_interval = 30            # 每30帧打印一次（约1秒）
        self.last_timeout_print = 0         # 上次超时打印时间

        # 帧率统计变量
        self.frame_count = 0                # 总帧数
        self.fps_start_time = 0             # FPS计算开始时间
        self.last_fps_time = 0              # 上次FPS计算时间
        self.current_fps = 0.0              # 当前帧率

        # 帧处理时间统计
        self.frame_start_time = 0           # 当前帧开始时间

        # 摄像头
        self.cap = None

        # 串口通信
        self.serial_comm = SerialComm('/dev/ttyTHS0', 115200, True)

        print("🚀 智能激光轨迹跟踪器已启动")
        print(f"控制周期: {self.control_cycle}ms")
        print(f"距离阈值: {self.distance_threshold}px")
        print(f"Y轴偏移: +{self.y_offset}px")

        # 初始化串口连接
        self.init_serial_connection()

    def init_serial_connection(self):
        """初始化串口连接"""
        try:
            connection = self.serial_comm.init()
            if connection:
                print("✅ 串口连接成功")
                return True
            else:
                print("❌ 串口连接失败")
                return False
        except Exception as e:
            print(f"❌ 串口初始化错误: {e}")
            return False

    def load_parameters(self):
        """从阈值文件加载参数（与gray_processor.py保持一致）"""
        try:
            # 检查文件是否存在
            if os.path.exists("thresholds_灰度.txt"):
                with open("thresholds_灰度.txt", "r") as f:
                    lines = f.readlines()

                    # 读取灰度阈值
                    self.gray_min = int(lines[0].strip())
                    self.gray_max = int(lines[1].strip())

                    # 读取ROI
                    self.roi = [int(x) for x in lines[2].strip().split(",")]

                    # 读取亮度值
                    if len(lines) > 3:
                        self.brightness = int(lines[3].strip())

                    # 读取对比度值
                    if len(lines) > 4:
                        self.contrast = int(lines[4].strip())

                print(f"✅ 已加载灰度参数: ({self.gray_min}-{self.gray_max})")
                print(f"📍 ROI: {self.roi}, 亮度: {self.brightness}, 对比度: {self.contrast/100.0:.1f}")
            else:
                print("⚠️ 阈值文件不存在，使用默认参数")
        except Exception as e:
            print(f"❌ 加载参数时出错: {str(e)}")
            print("使用默认参数")

    def set_y_offset(self, offset):
        """设置Y轴偏移量"""
        self.y_offset = offset
        print(f"📐 Y轴偏移设置为: +{self.y_offset}px")
    
    def load_trajectory_file(self, filename=None):
        """加载目标轨迹文件"""
        if filename is None:
            files = glob.glob("laser_coordinates_*.txt")
            if not files:
                print("❌ 未找到轨迹坐标文件")
                return False
            filename = max(files, key=os.path.getctime)
            print(f"📁 自动选择最新轨迹文件: {filename}")
        
        try:
            self.target_trajectory = []
            with open(filename, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('#') or not line:
                        continue
                    x, y = map(float, line.split(','))
                    # 应用Y轴偏移
                    y_adjusted = y + self.y_offset
                    self.target_trajectory.append([x, y_adjusted])
            
            self.total_points = len(self.target_trajectory)
            print(f"✅ 成功加载 {self.total_points} 个目标轨迹点")
            print(f"📐 Y轴偏移: +{self.y_offset}像素")

            if self.total_points > 0:
                print(f"起点: ({self.target_trajectory[0][0]:.1f}, {self.target_trajectory[0][1]:.1f}) [已偏移]")
                print(f"终点: ({self.target_trajectory[-1][0]:.1f}, {self.target_trajectory[-1][1]:.1f}) [已偏移]")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载轨迹文件失败: {e}")
            return False
    
    def init_camera(self):
        """初始化摄像头"""
        self.cap = cv2.VideoCapture(0)
        if not self.cap.isOpened():
            print("❌ 无法打开摄像头")
            return False
        
        print("✅ 摄像头初始化成功")
        return True
    
    def adjust_brightness_contrast(self, image, brightness=0, contrast=1.0):
        """调节图像亮度和对比度"""
        try:
            adjusted = cv2.convertScaleAbs(image, alpha=contrast, beta=brightness)
            return adjusted
        except Exception as e:
            return image
    
    def analyze_laser_color(self, image, contour):
        """分析轮廓内部区域，简单直接地比较RGB通道值来判断是红激光还是绿激光
        完全复刻gray_processor.py的实现
        参数:
            image: 输入图像
            contour: 轮廓点集
        返回: "RED", "GREEN" 或 "UNKNOWN"
        """
        # 创建轮廓掩码
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        cv2.drawContours(mask, [contour], 0, 255, -1)  # 填充轮廓内部为白色(255)

        # 使用掩码提取轮廓内的像素
        masked_image = cv2.bitwise_and(image, image, mask=mask)

        # 计算轮廓内的非零像素数量
        non_zero_pixels = cv2.countNonZero(mask)
        if non_zero_pixels == 0:
            return "UNKNOWN"

        # 获取各通道的总和，但只统计轮廓内部的像素
        b_sum = int(np.sum(masked_image[:, :, 0]))
        g_sum = int(np.sum(masked_image[:, :, 1]))
        r_sum = int(np.sum(masked_image[:, :, 2]))

        # 记录调试信息（减少打印频率）
        if self.print_counter % 100 == 0:  # 每100帧打印一次
            print(f"轮廓内部像素统计 - 红色总和: {r_sum}, 绿色总和: {g_sum}, 蓝色总和: {b_sum}")

            return "RED"

    
    def detect_red_laser_position(self, frame):
        """检测红色激光点位置 - 完全复刻gray_processor.py的实现"""
        if frame is None:
            return None

        # 复制帧以避免修改原始数据（与gray_processor保持一致）
        frame = frame.copy()
        orig_frame = frame.copy()

        # 应用亮度和对比度调节
        contrast_factor = self.contrast / 100.0  # 转换为0.5-3.0范围
        frame = self.adjust_brightness_contrast(frame, self.brightness, contrast_factor)

        # 应用ROI
        x1, y1, x2, y2 = self.roi
        height, width = frame.shape[:2]

        # 确保坐标在图像范围内
        x1 = max(0, min(x1, width-1))
        y1 = max(0, min(y1, height-1))
        x2 = max(0, min(x2, width-1))
        y2 = max(0, min(y2, height-1))

        # 检查ROI是否有效
        if x2 <= x1 or y2 <= y1:
            return None

        # 提取ROI，不进行缩放操作
        roi_frame = orig_frame[y1:y2, x1:x2].copy()

        if roi_frame.size == 0:
            return None

        # 转换为灰度图
        gray_roi = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2GRAY)

        # 应用灰度阈值处理
        binary = cv2.inRange(gray_roi, self.gray_min, self.gray_max)

        # 如果启用反转，则应用反转
        if self.invert:
            binary = cv2.bitwise_not(binary)

        # 对二值化结果进行膨胀 - 始终应用膨胀
        kernel = np.ones((self.dilate_kernel_size, self.dilate_kernel_size), np.uint8)
        binary = cv2.dilate(binary, kernel, iterations=self.dilate_iterations)

        # 寻找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 对每个轮廓进行处理
        for contour in contours:
            # 仅处理合适面积的轮廓（排除太小和太大的区域）
            contour_area = cv2.contourArea(contour)
            if 5 < contour_area < 500:  # 激光点面积范围：5-500像素
                # 获取原始外接矩形
                orig_x, orig_y, orig_w, orig_h = cv2.boundingRect(contour)

                # 缩小矩形尺寸（保留中心30%的区域）
                shrink_factor = 0.3
                new_w = max(3, int(orig_w * shrink_factor))  # 至少3像素宽
                new_h = max(3, int(orig_h * shrink_factor))  # 至少3像素高

                # 计算缩小后的矩形位置（保持中心不变）
                x = orig_x + (orig_w - new_w) // 2
                y = orig_y + (orig_h - new_h) // 2
                w = new_w
                h = new_h

                # 分析激光颜色 - 只统计轮廓内部的像素
                laser_type = self.analyze_laser_color(roi_frame, contour)

                # 计算激光点的中心坐标（相对于ROI）
                center_x = x + w // 2
                center_y = y + h // 2

                # 转换为全局坐标（相对于整个图像）
                global_x = x1 + center_x
                global_y = y1 + center_y

                # 返回所有激光点的坐标（红色、绿色或未知）
                if laser_type == "RED":
                    if self.print_counter % 100 == 0:  # 减少打印频率
                        print(f"🔴 红色激光点坐标: ({global_x}, {global_y}) - ROI内坐标: ({center_x}, {center_y}) - 面积: {contour_area:.0f}")

                    self.last_detection_time = time.time() * 1000
                    return [global_x, global_y]
                elif laser_type == "GREEN":
                    if self.print_counter % 100 == 0:  # 减少打印频率
                        print(f"🟢 绿色激光点坐标: ({global_x}, {global_y}) - ROI内坐标: ({center_x}, {center_y}) - 面积: {contour_area:.0f}")

                    self.last_detection_time = time.time() * 1000
                    return [global_x, global_y]
                else:  # UNKNOWN 或其他颜色
                    if self.print_counter % 100 == 0:  # 减少打印频率
                        print(f"⚪ 未知激光点坐标: ({global_x}, {global_y}) - ROI内坐标: ({center_x}, {center_y}) - 面积: {contour_area:.0f}")

                    self.last_detection_time = time.time() * 1000
                    return [global_x, global_y]

        return None
    
    def calculate_distance(self, pos1, pos2):
        """计算两点间距离"""
        if pos1 is None or pos2 is None:
            return float('inf')
        return np.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
    
    def should_switch_target(self):
        """判断是否切换到下一个目标点"""
        if not self.is_tracking or self.current_laser_position is None:
            return False
        
        current_time = time.time() * 1000
        elapsed_time = current_time - self.last_switch_time
        
        # 计算距离
        distance = self.calculate_distance(self.current_laser_position, self.target_position)
        
        # 判断条件
        distance_ok = distance < self.distance_threshold
        min_time_ok = elapsed_time > self.min_time_per_point
        max_time_exceeded = elapsed_time > self.max_time_per_point
        
        return (distance_ok and min_time_ok) or max_time_exceeded
    
    def switch_to_next_target(self):
        """切换到下一个目标点"""
        if self.current_target_index >= len(self.target_trajectory) - 1:
            self.is_tracking = False
            total_time = (time.time() * 1000 - self.start_time) / 1000
            print(f"\n🎉 轨迹跟踪完成！总用时: {total_time:.1f}秒")
            return False
        
        self.current_target_index += 1
        self.target_position = self.target_trajectory[self.current_target_index].copy()
        self.last_switch_time = time.time() * 1000
        
        progress = (self.current_target_index / self.total_points) * 100
        # 只在关键节点打印目标切换信息
        if self.current_target_index % 10 == 0 or self.current_target_index == self.total_points - 1:
            print(f"📍 目标 {self.current_target_index + 1}/{self.total_points} "
                  f"({progress:.1f}%) -> ({self.target_position[0]:.1f}, {self.target_position[1]:.1f})")
        
        return True
    
    def start_tracking(self):
        """开始轨迹跟踪"""
        if not self.target_trajectory:
            print("❌ 没有目标轨迹数据")
            return False

        self.current_target_index = 0
        self.is_tracking = True
        self.tracking_started = False  # 初始状态：未开始循迹
        self.start_time = time.time() * 1000
        self.last_switch_time = self.start_time

        # 初始化FPS计算
        self.frame_count = 0
        self.fps_start_time = time.time()
        self.last_fps_time = self.fps_start_time

        # 设置起始点和目标点
        self.start_position = self.target_trajectory[0].copy()
        self.target_position = self.target_trajectory[0].copy()

        print("\n🎯 智能轨迹跟踪已准备")
        print(f"目标轨迹点数: {self.total_points}")
        print(f"起始点: ({self.start_position[0]:.1f}, {self.start_position[1]:.1f})")
        print(f"等待激光点接近起始点（距离<{self.start_distance_threshold}px）...")
        print("=" * 60)

        return True
    
    def get_control_command(self, frame):
        """获取30ms周期的控制命令"""
        if not self.is_tracking:
            return None

        # 记录帧处理开始时间
        self.frame_start_time = time.time() * 1000

        # 检测当前激光位置
        detected_position = self.detect_red_laser_position(frame)
        if detected_position:
            self.current_laser_position = detected_position

        # 检查激光检测超时
        current_time = time.time() * 1000
        laser_timeout = current_time - self.last_detection_time > 1000  # 1秒超时

        # 减少超时打印频率
        if laser_timeout and (current_time - self.last_timeout_print > 3000):  # 3秒打印一次
            print("⚠️ 激光检测超时")
            self.last_timeout_print = current_time

        # 检查是否开始循迹
        if not self.tracking_started and self.current_laser_position is not None:
            # 计算激光点到起始点的距离
            distance_to_start = self.calculate_distance(self.current_laser_position, self.start_position)

            if distance_to_start < self.start_distance_threshold:
                # 激光点接近起始点，开始循迹
                self.tracking_started = True
                self.last_switch_time = current_time
                print(f"🚀 激光点已接近起始点（距离: {distance_to_start:.1f}px），开始循迹！")
            else:
                # 不打印等待信息，减少控制台输出
                pass

        # 如果已开始循迹，进行正常的目标切换逻辑
        if self.tracking_started:
            time_exceeded = current_time - self.last_switch_time > self.max_time_per_point

            # 如果超过最大停留时间，强制切换到下一个目标点
            if time_exceeded:
                if self.print_counter % 10 == 0:  # 减少超时切换打印
                    print(f"⏰ 超时切换目标点 (停留时间: {(current_time - self.last_switch_time):.0f}ms)")
                if not self.switch_to_next_target():
                    return None
            # 如果有激光检测，进行正常的距离判断切换
            elif self.current_laser_position is not None:
                if self.should_switch_target():
                    if not self.switch_to_next_target():
                        return None

        # 确定目标位置
        if not self.tracking_started:
            # 未开始循迹时，目标位置为起始点
            current_target = self.start_position
            target_index = 0
            progress = 0.0
        else:
            # 已开始循迹时，目标位置为当前轨迹点
            current_target = self.target_position
            target_index = self.current_target_index
            progress = round((self.current_target_index / self.total_points) * 100, 1)

        # 生成控制命令（无论是否检测到激光点）
        if self.current_laser_position:
            distance = self.calculate_distance(self.current_laser_position, current_target)
            current_pos = {
                "x": round(self.current_laser_position[0], 2),
                "y": round(self.current_laser_position[1], 2)
            }
        else:
            distance = float('inf')  # 未检测到激光点时距离设为无穷大
            current_pos = {"x": 0, "y": 0}  # 默认位置

        # 计算帧处理时间
        frame_end_time = time.time() * 1000
        frame_processing_time = frame_end_time - self.frame_start_time

        command = {
            "current_position": current_pos,
            "target_position": {
                "x": round(current_target[0], 2),
                "y": round(current_target[1], 2)
            },
            "target_index": target_index,
            "progress_percent": progress,
            "distance_to_target": round(distance, 2),
            "laser_detected": self.current_laser_position is not None,
            "tracking_started": self.tracking_started,
            "frame_processing_time": round(frame_processing_time, 1),
            "timestamp": int(time.time() * 1000)
        }

        # 更新帧计数器和FPS计算
        self.print_counter += 1
        self.frame_count += 1

        # 每100帧计算一次FPS
        current_time_sec = time.time()
        if self.frame_count % 100 == 0:
            elapsed_time = current_time_sec - self.last_fps_time
            if elapsed_time > 0:
                self.current_fps = 100.0 / elapsed_time
            self.last_fps_time = current_time_sec

        return command
    
    def stop_tracking(self):
        """停止跟踪"""
        self.is_tracking = False

        # 计算并打印FPS统计
        if self.frame_count > 0 and self.fps_start_time > 0:
            total_time = time.time() - self.fps_start_time
            if total_time > 0:
                average_fps = self.frame_count / total_time
                print("\n" + "="*50)
                print("📊 性能统计")
                print("="*50)
                print(f"总帧数: {self.frame_count}")
                print(f"运行时间: {total_time:.1f}秒")
                print(f"平均帧率: {average_fps:.1f} FPS")
                print(f"当前帧率: {self.current_fps:.1f} FPS")
                print(f"平均帧间隔: {1000/average_fps:.1f}ms")
                print("="*50)

        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        # 关闭串口连接
        self.serial_comm.close()
        print("⏹️ 跟踪已停止")
    
    def run_visual_tracking(self):
        """运行可视化跟踪（用于调试和演示）"""
        if not self.init_camera():
            return
        
        if not self.start_tracking():
            return
        
        print("🔄 开始可视化跟踪...")
        
        # 创建显示窗口
        cv2.namedWindow('Laser Tracking', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Laser Tracking', 800, 600)
        
        cycle_count = 0
        last_cycle_time = time.time() * 1000
        
        while self.is_tracking:
            ret, frame = self.cap.read()
            if not ret:
                print("❌ 无法读取摄像头")
                break
            
            # 获取控制命令
            command = self.get_control_command(frame)

            # 发送当前激光点和目标位置到串口（30ms周期）- 使用04数据帧
            if self.is_tracking and self.target_position:
                frame_time = command.get('frame_processing_time', 0) if command else 0
                success = self.serial_comm.send_laser_and_target(self.current_laser_position, self.target_position, frame_time)
                if not success and cycle_count % 100 == 0:  # 每100周期提示一次错误
                    print("⚠️ 串口发送失败")

            # 绘制可视化信息
            display_frame = frame.copy()
            
            # 绘制ROI
            x1, y1, x2, y2 = self.roi
            cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制当前激光位置
            if self.current_laser_position:
                pos = self.current_laser_position
                cv2.circle(display_frame, (int(pos[0]), int(pos[1])), 8, (0, 0, 255), -1)
                cv2.putText(display_frame, f"Current: ({pos[0]:.0f},{pos[1]:.0f})", 
                           (int(pos[0])+10, int(pos[1])-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
            
            # 绘制目标位置
            if self.target_position:
                target = self.target_position
                cv2.circle(display_frame, (int(target[0]), int(target[1])), 8, (255, 0, 0), 2)
                cv2.putText(display_frame, f"Target: ({target[0]:.0f},{target[1]:.0f})", 
                           (int(target[0])+10, int(target[1])+10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
            
            # 显示状态信息
            if command:
                laser_status = "✅" if command['laser_detected'] else "❌"
                status_text = f"Progress: {command['progress_percent']:.1f}% | Distance: {command['distance_to_target']:.1f}px | Laser: {laser_status}"
                cv2.putText(display_frame, status_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                # 显示FPS信息
                fps_text = f"FPS: {self.current_fps:.1f} | Frame: {self.frame_count}"
                cv2.putText(display_frame, fps_text, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                # 每100个周期打印一次 (约3秒)
                if cycle_count % 100 == 0:
                    laser_info = f"激光{'检测' if command['laser_detected'] else '丢失'}"
                    print(f"周期 {cycle_count:3d}: {laser_info} | "
                          f"当前({command['current_position']['x']:6.1f}, {command['current_position']['y']:6.1f}) -> "
                          f"目标({command['target_position']['x']:6.1f}, {command['target_position']['y']:6.1f}) | "
                          f"距离: {command['distance_to_target']:5.1f}px")
            
            cv2.imshow('Laser Tracking', display_frame)
            
            # 控制周期
            current_time = time.time() * 1000
            elapsed = current_time - last_cycle_time
            if elapsed < self.control_cycle:
                time.sleep((self.control_cycle - elapsed) / 1000.0)
            
            last_cycle_time = time.time() * 1000
            cycle_count += 1
            
            # 检查退出
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q') or key == 27:  # q键或ESC退出
                break
        
        self.stop_tracking()
        print(f"总周期数: {cycle_count}")

    def run_control_mode(self):
        """运行控制模式（无可视化，纯控制）"""
        if not self.init_camera():
            return

        if not self.start_tracking():
            return

        print("🎯 开始控制模式（无可视化）...")
        print("按 Ctrl+C 停止")

        cycle_count = 0
        last_cycle_time = time.time() * 1000

        try:
            while self.is_tracking:
                ret, frame = self.cap.read()
                if not ret:
                    print("❌ 无法读取摄像头")
                    break

                # 获取控制命令
                command = self.get_control_command(frame)

                # 发送当前激光点和目标位置到串口（30ms周期）- 使用04数据帧
                if self.is_tracking and self.target_position:
                    frame_time = command.get('frame_processing_time', 0) if command else 0
                    success = self.serial_comm.send_laser_and_target(self.current_laser_position, self.target_position, frame_time)
                    if not success and cycle_count % 100 == 0:  # 每100周期提示一次错误
                        print("⚠️ 串口发送失败")

                # 每50个周期打印一次状态
                if command and cycle_count % 50 == 0:
                    laser_info = f"激光{'检测' if command['laser_detected'] else '丢失'}"
                    print(f"周期 {cycle_count:4d}: {laser_info} | "
                          f"进度 {command['progress_percent']:5.1f}% | "
                          f"当前({command['current_position']['x']:6.1f}, {command['current_position']['y']:6.1f}) -> "
                          f"目标({command['target_position']['x']:6.1f}, {command['target_position']['y']:6.1f}) | "
                          f"距离: {command['distance_to_target']:5.1f}px")

                # 控制周期
                current_time = time.time() * 1000
                elapsed = current_time - last_cycle_time
                if elapsed < self.control_cycle:
                    time.sleep((self.control_cycle - elapsed) / 1000.0)

                last_cycle_time = time.time() * 1000
                cycle_count += 1

        except KeyboardInterrupt:
            print("\n👋 用户中断控制")

        self.stop_tracking()
        print(f"总周期数: {cycle_count}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 智能激光轨迹跟踪器")
    print("=" * 60)
    
    tracker = SmartLaserTracker()
    
    # 加载参数
    tracker.load_parameters()
    
    # 加载轨迹文件
    if not tracker.load_trajectory_file():
        print("\n💡 使用方法:")
        print("1. 先运行 111 (1).py 识别图形并导出坐标")
        print("2. 再运行此程序进行智能激光跟踪")
        return
    
    print("\n📋 操作选项:")
    print("1. 可视化跟踪模式（调试用）")
    print("2. 控制模式（实际控制，无可视化）")
    print("3. 退出程序")

    try:
        user_input = input("\n请选择操作 (1/2/3): ").strip()

        if user_input == '1' or user_input == '':
            tracker.run_visual_tracking()
        elif user_input == '2':
            tracker.run_control_mode()
        else:            print("👋 程序退出")
            
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
        tracker.stop_tracking()

if __name__ == "__main__":
    main()
