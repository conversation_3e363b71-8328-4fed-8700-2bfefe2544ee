"""
RGB颜色处理器 - 基于RGB颜色空间的图像处理和激光点检测

功能描述:
- 使用RGB颜色阈值进行二值化处理
- 支持从配置文件加载参数
- 支持ROI区域选择
- 实时摄像头图像处理和显示

作者: [作者名]
创建时间: [创建时间]
"""

import cv2
import numpy as np
import os

# ==================== 全局参数配置 ====================
# RGB颜色阈值范围 - 可根据实际需要调整
R_MIN = 0      # 红色通道最小值
R_MAX = 255    # 红色通道最大值
G_MIN = 0      # 绿色通道最小值
G_MAX = 255    # 绿色通道最大值
B_MIN = 0      # 蓝色通道最小值
B_MAX = 255    # 蓝色通道最大值

# 摄像头设置
EXPOSURE = -7  # 摄像头曝光度设置

# ROI (Region of Interest) 感兴趣区域设置
ROI = [250, 185, 450, 320]  # 格式: [x1, y1, x2, y2] 左上角和右下角坐标

# 图像处理选项
INVERT = False  # 是否反转二值化结果

# ==================== 参数加载函数 ====================
def load_parameters():
    """
    从配置文件加载RGB处理参数

    文件格式 (thresholds_RGB.txt):
    第1行: RGB下限值，格式: r_min,g_min,b_min
    第2行: RGB上限值，格式: r_max,g_max,b_max
    第3行: ROI区域，格式: x1,y1,x2,y2
    第4行: 曝光度值 (可选)

    Returns:
        None - 直接修改全局变量
    """
    global R_MIN, R_MAX, G_MIN, G_MAX, B_MIN, B_MAX, EXPOSURE, ROI

    try:
        # 检查配置文件是否存在
        if os.path.exists("thresholds_RGB.txt"):
            with open("thresholds_RGB.txt", "r") as f:
                lines = f.readlines()

                # 解析RGB下限值 (第1行)
                rgb_lower = [int(x) for x in lines[0].strip().split(",")]
                # 解析RGB上限值 (第2行)
                rgb_upper = [int(x) for x in lines[1].strip().split(",")]

                # 设置RGB阈值参数
                R_MIN = rgb_lower[0]
                G_MIN = rgb_lower[1]
                B_MIN = rgb_lower[2]

                R_MAX = rgb_upper[0]
                G_MAX = rgb_upper[1]
                B_MAX = rgb_upper[2]

                # 解析ROI区域 (第3行)
                ROI = [int(x) for x in lines[2].strip().split(",")]

                # 解析曝光度 (第4行，可选)
                if len(lines) > 3:
                    EXPOSURE = int(lines[3].strip())

                # 输出加载成功信息
                print(f"已从文件加载RGB参数: R({R_MIN}-{R_MAX}), G({G_MIN}-{G_MAX}), B({B_MIN}-{B_MAX})")
                print(f"ROI: {ROI}, 曝光度: {EXPOSURE}")
        else:
            print("阈值文件不存在，使用默认参数")
    except Exception as e:
        print(f"加载参数时出错: {str(e)}")
        print("使用默认参数")

# ==================== 图像处理函数 ====================
def process_frame(frame):
    """
    处理单帧图像，应用RGB颜色空间的二值化处理

    处理流程:
    1. 输入验证和帧复制
    2. ROI区域提取和缩放显示
    3. RGB通道分离和阈值处理
    4. 掩码合并和结果生成

    Args:
        frame: 输入的BGR格式图像帧

    Returns:
        numpy.ndarray: 处理后的二值化结果图像 (BGR格式)
    """
    # 输入验证 - 防止空帧导致程序崩溃
    if frame is None or frame.size == 0:
        return np.zeros((480, 640, 3), dtype=np.uint8)

    # 复制帧以避免修改原始数据
    frame = frame.copy()
    orig_frame = frame.copy()

    # ========== ROI区域处理 ==========
    if ROI is not None:
        x1, y1, x2, y2 = ROI
        height, width = frame.shape[:2]

        # 坐标边界检查 - 确保ROI坐标在图像范围内
        x1 = max(0, min(x1, width-1))
        y1 = max(0, min(y1, height-1))
        x2 = max(0, min(x2, width-1))
        y2 = max(0, min(y2, height-1))

        # 验证ROI有效性
        if x2 > x1 and y2 > y1:
            # 提取感兴趣区域
            roi = frame[y1:y2, x1:x2]

            # 创建白色背景用于显示
            roi_display = np.ones_like(orig_frame) * 255

            # 计算缩放比例以保持ROI的宽高比
            roi_height, roi_width = roi.shape[:2]
            scale_factor = min(width / roi_width, height / roi_height)

            # 计算缩放后的尺寸
            new_width = int(roi_width * scale_factor)
            new_height = int(roi_height * scale_factor)

            # 执行ROI缩放和居中显示
            if new_width > 0 and new_height > 0:
                resized_roi = cv2.resize(roi, (new_width, new_height))

                # 计算居中放置的起始坐标
                start_x = (width - new_width) // 2
                start_y = (height - new_height) // 2

                # 将缩放后的ROI放置到白色背景上
                roi_display[start_y:start_y+new_height, start_x:start_x+new_width] = resized_roi
                frame = roi_display

    # ========== RGB颜色空间二值化处理 ==========
    # 分离BGR通道 (OpenCV使用BGR而非RGB顺序)
    b, g, r = cv2.split(frame)

    # 对每个颜色通道应用阈值处理
    r_mask = cv2.inRange(r, R_MIN, R_MAX)  # 红色通道掩码
    g_mask = cv2.inRange(g, G_MIN, G_MAX)  # 绿色通道掩码
    b_mask = cv2.inRange(b, B_MIN, B_MAX)  # 蓝色通道掩码

    # 合并所有通道掩码 - 使用逻辑AND操作
    # 只有当所有通道都在阈值范围内时，像素才会被保留
    mask = cv2.bitwise_and(r_mask, g_mask)
    mask = cv2.bitwise_and(mask, b_mask)

    # 将灰度掩码转换为彩色图像用于显示
    result = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)

    # 可选的图像反转处理
    if INVERT:
        result = cv2.bitwise_not(result)

    return result

# ==================== 主程序函数 ====================
def main():
    """
    主程序入口函数

    功能流程:
    1. 加载配置参数
    2. 初始化摄像头
    3. 设置摄像头参数
    4. 实时图像处理循环
    5. 显示处理结果
    6. 资源清理
    """
    # 步骤1: 加载配置参数
    load_parameters()

    # 步骤2: 初始化摄像头 (使用默认摄像头设备0)
    cap = cv2.VideoCapture(0)

    # 步骤3: 设置摄像头曝光参数
    try:
        cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0)  # 关闭自动曝光
        cap.set(cv2.CAP_PROP_EXPOSURE, EXPOSURE)  # 设置手动曝光值
        print(f"摄像头曝光度设置为 {EXPOSURE}")
    except:
        print("设置曝光度失败，使用默认曝光")

    # 构建参数显示文本
    param_text = f"RGB模式 R:({R_MIN}-{R_MAX}) G:({G_MIN}-{G_MAX}) B:({B_MIN}-{B_MAX}) 曝光:{EXPOSURE}"
    print(param_text)

    # 步骤4: 主处理循环
    while True:
        # 从摄像头读取一帧图像
        ret, frame = cap.read()
        if not ret:
            print("无法读取摄像头")
            break

        # 应用RGB处理算法
        result = process_frame(frame)

        # 在原始图像上添加参数信息文本
        cv2.putText(frame, param_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        # 步骤5: 显示处理结果
        cv2.imshow("RGB模式 - 原始", frame)      # 显示原始图像
        cv2.imshow("RGB模式 - 处理后", result)   # 显示处理后的二值化结果

        # 按键检测
        key = cv2.waitKey(30)
        if key == 27:  # ESC键退出程序
            break

    # 步骤6: 资源清理
    cap.release()           # 释放摄像头资源
    cv2.destroyAllWindows() # 关闭所有OpenCV窗口


# ==================== 程序入口 ====================
if __name__ == "__main__":
    main()