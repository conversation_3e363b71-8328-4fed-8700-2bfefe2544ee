from maix import image, display, app, time, camera
import cv2
import numpy as np
from micu_uart_lib import (
    SimpleUART, micu_printf
)

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)  # 形态学处理核
        
    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        # 紫色HSV范围检测（可根据实际调整）
        lower_purple = np.array([130, 80, 80])
        upper_purple = np.array([160, 255, 255])
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []
        
        for cnt in contours_purple:
            # 计算激光点中心
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            laser_points.append((cx, cy))
            # 绘制激光点标记
            cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        return img, laser_points

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 初始化显示和摄像头
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    
    # 初始化激光检测器和串口
    laser_detector = PurpleLaserDetector()
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("$$", "##", True)  # 设置帧格式
    else:
        print("串口初始化失败")
        exit()

    # 定义参数
    min_contour_area = 3000
    max_contour_area = 40000
    target_sides = 4

    while not app.need_exit():
        # 从摄像头读取图像
        img = cam.read()
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        output = img_cv.copy()

        # 1. 矩形检测与处理
        # 转灰度并二值化
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 按面积排序（外框在前，内框在后）
        quads.sort(key=lambda x: x[1], reverse=True)
        
        # 移除最大的外框（只保留内框）
        inner_quads = quads[1:]
        
        # 处理内框
        inner_centers = []
        for approx, area in inner_quads:
            # 计算并绘制中心点
            M = cv2.moments(approx)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                inner_centers.append((cx, cy))
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)  # 蓝色中心点
                # 绘制内框轮廓（可选）
                cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

        # 2. 激光检测
        output, laser_points = laser_detector.detect(output)

        # 3. 串口发送数据
        # 发送内框中心点（格式：I,数量,中心1x,中心1y,中心2x,中心2y...）
        if inner_centers:
            inner_data = f"I,{len(inner_centers)}"
            for (cx, cy) in inner_centers:
                inner_data += f",{cx},{cy}"
            micu_printf(inner_data)
        
        # 发送激光点（格式：L,数量,点1x,点1y,点2x,点2y...）
        if laser_points:
            laser_data = f"L,{len(laser_points)}"
            for (x, y) in laser_points:
                laser_data += f",{x},{y}"
            micu_printf(laser_data)

        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)