#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口通信测试脚本
测试smart_laser_tracker.py中的串口功能
"""

import time
import struct

class SerialCommTest:
    """串口通信测试类"""
    
    def __init__(self, port='/dev/ttyTHS0', baudrate=115200, enable=True):
        self.port = port
        self.baudrate = baudrate
        self.enable = enable
        self.connection = None
        
        # 指令类型定义
        self.CMD_RECT_VERTICES = 0x01  # 方框顶点坐标
        self.CMD_LASER_POINT = 0x02    # 激光点坐标
  
        
    def init(self):
        """初始化串口连接"""
        if not self.enable:
            print("串口发送功能已禁用")
            return None
            
        try:
            import serial
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"✅ 串口 {self.port} 已打开，波特率 {self.baudrate}")
            return self.connection
        except ImportError:
            print("❌ 未安装pyserial库: pip install pyserial")
            return None
        except Exception as e:
            print(f"❌ 无法打开串口 {self.port}: {e}")
            self.connection = None
            return None
    
    def create_packet(self, cmd, data):
        """创建串口通信数据包"""
        header = bytes([0xAA, 0x55])  # 帧头
        cmd_byte = bytes([cmd])       # 指令类型
        data_len = len(data)
        len_byte = bytes([data_len])  # 数据长度
        
        # 校验和计算
        checksum = cmd + data_len
        for byte in data:
            checksum += byte
        checksum = checksum & 0xFF
        checksum_byte = bytes([checksum])
        
        return header + cmd_byte + len_byte + data + checksum_byte
    
    def send_target_position(self, point):
        """发送目标位置坐标 - 使用03数据帧"""
        if self.connection is None or not self.enable or point is None:
            return False

        try:
            x = max(0, int(point[0]))
            y = max(0, int(point[1]))
            data = struct.pack('<HH', x, y)

            packet = self.create_packet(self.CMD_TARGET_POSITION, data)  # 使用03数据帧
            self.connection.write(packet)

            # 打印发送的数据包信息
            packet_hex = ' '.join([f'{b:02X}' for b in packet])
            print(f"📤 发送[03帧]: ({x}, {y}) -> [{packet_hex}]")
            return True

        except Exception as e:
            print(f"❌ 发送目标位置数据时出错: {e}")
            return False

    def send_laser_and_target(self, current_laser, target_point):
        """发送当前激光点和目标点坐标 - 使用04数据帧"""
        if self.connection is None or not self.enable:
            return False

        try:
            # 当前激光点坐标（如果没有检测到则使用0,0）
            if current_laser is not None:
                laser_x = max(0, int(current_laser[0]))
                laser_y = max(0, int(current_laser[1]))
            else:
                laser_x = 0
                laser_y = 0

            # 目标点坐标
            if target_point is not None:
                target_x = max(0, int(target_point[0]))
                target_y = max(0, int(target_point[1]))
            else:
                target_x = 0
                target_y = 0

            # 打包数据：当前激光点(4字节) + 目标点(4字节) = 8字节
            data = struct.pack('<HHHH', laser_x, laser_y, target_x, target_y)

            packet = self.create_packet(self.CMD_LASER_AND_TARGET, data)  # 使用04数据帧
            self.connection.write(packet)

            # 打印发送的数据包信息
            packet_hex = ' '.join([f'{b:02X}' for b in packet])
            print(f"📤 发送[04帧]: 激光({laser_x}, {laser_y}) + 目标({target_x}, {target_y}) -> [{packet_hex}]")
            return True

        except Exception as e:
            print(f"❌ 发送激光点和目标点数据时出错: {e}")
            return False

    def close(self):
        """关闭串口连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            print("🔌 串口已关闭")

def test_serial_communication():
    """测试串口通信功能"""
    print("=" * 50)
    print("🧪 串口通信测试")
    print("=" * 50)
    
    # 创建串口对象
    serial_comm = SerialCommTest('/dev/ttyTHS0', 115200, True)
    
    # 初始化串口
    if not serial_comm.init():
        print("❌ 串口初始化失败，测试终止")
        return
    
    print("\n🎯 开始发送测试数据...")

    # 测试数据：[当前激光点, 目标点]
    test_data = [
        [[50, 100], [100, 200]],    # 激光在(50,100), 目标(100,200)
        [[150, 250], [300, 400]],   # 激光在(150,250), 目标(300,400)
        [[350, 450], [500, 600]],   # 激光在(350,450), 目标(500,600)
        [None, [800, 300]],         # 未检测到激光, 目标(800,300)
        [[750, 350], [1000, 500]]   # 激光在(750,350), 目标(1000,500)
    ]

    try:
        for i, (laser_pos, target_pos) in enumerate(test_data):
            print(f"\n测试组合 {i+1}/5:")
            success = serial_comm.send_laser_and_target(laser_pos, target_pos)
            if success:
                laser_str = f"({laser_pos[0]}, {laser_pos[1]})" if laser_pos else "未检测"
                print(f"✅ 成功发送 - 激光: {laser_str}, 目标: ({target_pos[0]}, {target_pos[1]})")
            else:
                print(f"❌ 发送失败")

            time.sleep(0.5)  # 500ms间隔
            
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
    
    # 关闭串口
    serial_comm.close()
    print("\n✅ 串口通信测试完成")

if __name__ == "__main__":
    test_serial_communication()
