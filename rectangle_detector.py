import cv2
import numpy as np

class RectangleDetector:
    """矩形检测器类 - 专门用于检测A4纸等矩形物体"""
    
    def __init__(self, config=None):
        """初始化矩形检测器
        Args:
            config (dict): 配置参数字典，可选
        """
        # 默认配置参数
        self.config = {
            # HSV白色检测范围
            'lower_white_hsv': np.array([0, 0, 0]),      # 白色HSV下限
            'upper_white_hsv': np.array([250, 20, 250]), # 白色HSV上限
            
            # 面积筛选参数
            'min_area': 50,        # 最小面积
            'max_area': 100000,    # 最大面积
            
            # 长宽比参数
            'target_aspect_ratio': 1.414,  # A4纸标准长宽比(297mm/210mm)
            'aspect_tolerance': 0.2,       # 长宽比容差(±20%)
            
            # 形态学操作参数
            'do_morphology': True,         # 是否进行形态学操作
            'kernel_size': 3,              # 形态学核大小
            'morph_iterations': 1,         # 形态学操作迭代次数
            
            # 多边形逼近参数
            'approx_epsilon_ratio': 0.03,  # 逼近精度比例(相对于周长)
            
            # 显示参数
            'draw_contour': True,          # 是否绘制轮廓
            'draw_center': True,           # 是否绘制中心点
            'contour_color': (0, 255, 0),  # 轮廓颜色(绿色)
            'center_color': (0, 0, 255),   # 中心点颜色(红色)
            'text_color': (255, 255, 255), # 文字颜色(白色)
        }
        
        # 更新配置
        if config:
            self.config.update(config)
    
    def detect(self, img):
        """检测图像中的矩形
        Args:
            img: 输入图像(BGR格式)
        Returns:
            tuple: (处理后的图像, 检测到的矩形列表)
                   矩形格式: {'contour': 轮廓, 'corners': 四个角点, 'center': 中心点, 'area': 面积, 'aspect_ratio': 长宽比}
        """
        img_result = img.copy()
        rectangles = []
        
        # 1. 转换为HSV颜色空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 2. 提取白色区域
        white_mask = cv2.inRange(hsv, self.config['lower_white_hsv'], self.config['upper_white_hsv'])
        
        # 3. 形态学操作去噪
        if self.config['do_morphology']:
            kernel = np.ones((self.config['kernel_size'], self.config['kernel_size']), np.uint8)
            white_mask = cv2.dilate(white_mask, kernel, iterations=self.config['morph_iterations'])
            white_mask = cv2.erode(white_mask, kernel, iterations=self.config['morph_iterations'])
        
        # 4. 查找轮廓
        contours, _ = cv2.findContours(white_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 5. 筛选矩形候选区域
        candidates = []
        for contour in contours:
            rect_info = self._analyze_contour(contour)
            if rect_info:
                candidates.append(rect_info)
        
        # 6. 按面积排序，选择最大的矩形
        if candidates:
            candidates.sort(key=lambda x: x['area'], reverse=True)
            rectangles = candidates
            
            # 7. 绘制检测结果
            if self.config['draw_contour'] or self.config['draw_center']:
                self._draw_rectangles(img_result, rectangles)
        
        return img_result, rectangles
    
    def _analyze_contour(self, contour):
        """分析轮廓是否为有效矩形
        Args:
            contour: 输入轮廓
        Returns:
            dict: 矩形信息字典，如果不是有效矩形则返回None
        """
        # 计算面积
        area = cv2.contourArea(contour)
        
        # 面积筛选
        if not (self.config['min_area'] <= area <= self.config['max_area']):
            return None
        
        # 多边形逼近
        perimeter = cv2.arcLength(contour, True)
        epsilon = self.config['approx_epsilon_ratio'] * perimeter
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 检查是否为四边形
        if len(approx) != 4:
            return None
        
        # 计算最小外接矩形
        rect = cv2.minAreaRect(approx)
        width, height = rect[1]
        
        # 避免除零错误
        if width == 0 or height == 0:
            return None
        
        # 计算长宽比(考虑旋转)
        aspect_ratio = max(width, height) / min(width, height)
        
        # 长宽比验证
        target_ratio = self.config['target_aspect_ratio']
        tolerance = self.config['aspect_tolerance']
        if abs(aspect_ratio - target_ratio) > tolerance:
            return None
        
        # 提取四个角点并排序
        corners = self._sort_corners(approx.reshape(4, 2).astype(np.float32))
        
        # 计算中心点
        center = (int((corners[0][0] + corners[2][0]) / 2), 
                 int((corners[0][1] + corners[2][1]) / 2))
        
        return {
            'contour': contour,
            'approx': approx,
            'corners': corners,
            'center': center,
            'area': area,
            'aspect_ratio': aspect_ratio,
            'rect': rect
        }
    
    def _sort_corners(self, pts):
        """对四个角点进行排序
        Args:
            pts: 四个角点坐标
        Returns:
            np.array: 排序后的角点 [左上, 右上, 右下, 左下]
        """
        # 按坐标和排序
        s = pts.sum(axis=1)
        tl = pts[np.argmin(s)]  # 左上(x+y最小)
        br = pts[np.argmax(s)]  # 右下(x+y最大)
        
        # 按坐标差排序
        diff = np.diff(pts, axis=1)
        tr = pts[np.argmin(diff)]  # 右上(x-y最小)
        bl = pts[np.argmax(diff)]  # 左下(x-y最大)
        
        return np.array([tl, tr, br, bl], dtype=np.float32)
    
    def _draw_rectangles(self, img, rectangles):
        """在图像上绘制检测到的矩形
        Args:
            img: 目标图像
            rectangles: 矩形列表
        """
        for i, rect_info in enumerate(rectangles):
            # 绘制轮廓
            if self.config['draw_contour']:
                cv2.drawContours(img, [rect_info['contour']], -1, self.config['contour_color'], 2)
            
            # 绘制中心点
            if self.config['draw_center']:
                center = rect_info['center']
                cv2.circle(img, center, 3, self.config['center_color'], -1)
            
            # 绘制标签
            corners = rect_info['corners']
            label_pos = (int(corners[0][0]), int(corners[0][1] - 10))
            label_text = f"Rectangle {i+1}"
            cv2.putText(img, label_text, label_pos, cv2.FONT_HERSHEY_SIMPLEX, 
                       0.5, self.config['text_color'], 1)
    
    def get_largest_rectangle(self, img):
        """获取图像中最大的矩形
        Args:
            img: 输入图像
        Returns:
            dict: 最大矩形信息，如果没有检测到则返回None
        """
        _, rectangles = self.detect(img)
        return rectangles[0] if rectangles else None
    
    def update_config(self, new_config):
        """更新配置参数
        Args:
            new_config (dict): 新的配置参数
        """
        self.config.update(new_config)
    
    def get_config(self):
        """获取当前配置参数
        Returns:
            dict: 当前配置参数
        """
        return self.config.copy()


# 使用示例和测试代码
if __name__ == "__main__":
    # 创建检测器实例
    detector = RectangleDetector()
    
    # 自定义配置示例
    custom_config = {
        'min_area': 100,
        'max_area': 50000,
        'target_aspect_ratio': 1.5,  # 自定义长宽比
        'aspect_tolerance': 0.3,
        'contour_color': (255, 0, 0),  # 蓝色轮廓
    }
    
    # 更新配置
    detector.update_config(custom_config)
    
    print("矩形检测器已创建")
    print("当前配置:", detector.get_config())
    print("\n使用方法:")
    print("1. img_result, rectangles = detector.detect(img)")
    print("2. largest_rect = detector.get_largest_rectangle(img)")
