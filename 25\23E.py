import cv2
import numpy as np
import os
import math
import serial
import struct
import time

# 使用CPU模式
USE_GPU = False
print("使用CPU模式进行图像处理")

# 全局参数
GRAY_MIN = 224
GRAY_MAX = 255
BRIGHTNESS = 0  # 亮度调节值 (-100 到 100)
CONTRAST = 100  # 对比度调节值 (50 到 300，对应0.5到3.0倍)
ROI = [95,115,568,464]  # x1, y1, x2, y2
INVERT = False

# 控制是否读取阈值文件
USE_THRESHOLD_FILE = True

# 膨胀参数
DILATE_KERNEL_SIZE = 2
DILATE_ITERATIONS = 2

# 矩形检测参数
MIN_CONTOUR_AREA = 1000
MAX_CONTOUR_AREA = 30000
TARGET_SIDES = 4

# 串口通信参数
SERIAL_PORT = '/dev/ttyTHS0'  # 串口设备路径
SERIAL_BAUDRATE = 115200      # 波特率
ENABLE_SERIAL = True          # 是否启用串口发送

# 显示控制参数
ENABLE_DISPLAY =   False       # 是否显示图像窗口
ENABLE_PRINT_COORDS = True   # 是否打印坐标信息（减少I/O提高性能）
ENABLE_RECT_DETECTION = True  # 是否启用矩形检测（可禁用以提高性能）
ENABLE_VISUAL_ANNOTATIONS = True   # 是否绘制可视化标注
ENABLE_CONTROL_PANEL = True   # 是否显示控制面板

# 指令类型定义
CMD_RECT_VERTICES = 0x01  # 方框顶点坐标
CMD_LASER_POINT = 0x02    # 激光点坐标

# 串口对象
serial_connection = None

# 发送频率控制
last_rect_send_time = 0     # 上次发送矩形的时间
RECT_SEND_INTERVAL = 0.5    # 矩形发送间隔 500ms
# 激光点不再使用时间间隔控制，每帧都发送（33.3 Hz）

def init_serial():
    """初始化串口连接"""
    global serial_connection

    if not ENABLE_SERIAL:
        print("串口发送功能已禁用")
        return None

    try:
        serial_connection = serial.Serial(SERIAL_PORT, SERIAL_BAUDRATE, timeout=1)
        print(f"串口 {SERIAL_PORT} 已打开，波特率 {SERIAL_BAUDRATE}")
        return serial_connection
    except serial.SerialException as e:
        print(f"无法打开串口 {SERIAL_PORT}: {e}")
        serial_connection = None
        return None

def create_packet(cmd, data):
    """创建串口通信数据包

    数据包格式：
    [帧头2字节][指令1字节][数据长度1字节][数据N字节][校验和1字节]
    """
    # 帧头
    header = bytes([0xAA, 0x55])
    # 指令类型
    cmd_byte = bytes([cmd])
    # 数据长度
    data_len = len(data)
    len_byte = bytes([data_len])
    # 校验和计算
    checksum = cmd + data_len
    for byte in data:
        checksum += byte
    checksum = checksum & 0xFF  # 取低8位
    checksum_byte = bytes([checksum])

    # 组合数据包
    packet = header + cmd_byte + len_byte + data + checksum_byte
    return packet

def send_rect_midpoints(midpoints):
    """发送矩形框四个中点坐标（带频率控制）

    Args:
        midpoints: 中点坐标列表，格式为 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
    """
    global serial_connection, last_rect_send_time

    if serial_connection is None or not ENABLE_SERIAL or midpoints is None:
        return

    # 检查发送间隔
    current_time = time.time()
    if current_time - last_rect_send_time < RECT_SEND_INTERVAL:
        return  # 还未到发送时间

    if len(midpoints) != 4:
        print(f"警告: 矩形中点数量不正确，期望4个，实际{len(midpoints)}个")
        return

    try:
        # 打包数据 (4个点，每个点2个坐标，每个坐标2字节)
        data = bytes()
        for (x, y) in midpoints:
            # 使用小端字节序，确保坐标为正整数
            x = max(0, int(x))
            y = max(0, int(y))
            data += struct.pack('<HH', x, y)

        packet = create_packet(CMD_RECT_VERTICES, data)
        serial_connection.write(packet)
        last_rect_send_time = current_time  # 更新发送时间

        # 打印时间标志
        timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
        milliseconds = int((current_time % 1) * 1000)
        print(f"[{timestamp}.{milliseconds:03d}] 串口发送矩形中点: {midpoints}")

    except Exception as e:
        print(f"发送矩形中点数据时出错: {e}")

def send_red_laser_point(point):
    """发送红色激光点坐标（每帧发送，33.3 Hz）

    Args:
        point: 激光点坐标，格式为 (x, y)
    """
    global serial_connection

    if serial_connection is None or not ENABLE_SERIAL or point is None:
        return

    try:
        # 确保坐标为正整数
        x = max(0, int(point[0]))
        y = max(0, int(point[1]))

        # 打包数据 (2个坐标，每个2字节)
        data = struct.pack('<HH', x, y)

        packet = create_packet(CMD_LASER_POINT, data)
        serial_connection.write(packet)

        # 打印时间标志
        current_time = time.time()
        timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
        milliseconds = int((current_time % 1) * 1000)
        print(f"[{timestamp}.{milliseconds:03d}] 串口发送红色激光点: ({x}, {y})")

    except Exception as e:
        print(f"发送激光点数据时出错: {e}")

def close_serial():
    """关闭串口连接"""
    global serial_connection

    if serial_connection:
        serial_connection.close()
        serial_connection = None
        print("串口已关闭")

def load_parameters():
    """从阈值文件加载参数"""
    global GRAY_MIN, GRAY_MAX, BRIGHTNESS, CONTRAST, ROI

    if not USE_THRESHOLD_FILE:
        print("不读取阈值文件，使用全局参数")
        return

    try:
        if os.path.exists("thresholds_灰度.txt"):
            with open("thresholds_灰度.txt", "r") as f:
                lines = f.readlines()
                GRAY_MIN = int(lines[0].strip())
                GRAY_MAX = int(lines[1].strip())
                ROI = [int(x) for x in lines[2].strip().split(",")]
                if len(lines) > 3:
                    BRIGHTNESS = int(lines[3].strip())
                if len(lines) > 4:
                    CONTRAST = int(lines[4].strip())
                print(f"已加载参数: Gray({GRAY_MIN}-{GRAY_MAX}) 亮度:{BRIGHTNESS} 对比度:{CONTRAST/100.0:.1f}")
        else:
            print("阈值文件不存在，使用默认参数")
    except Exception as e:
        print(f"加载参数时出错: {str(e)}")

def adjust_brightness_contrast(image, brightness, contrast):
    """调整图像亮度和对比度"""
    alpha = contrast / 100.0
    beta = brightness
    return cv2.convertScaleAbs(image, alpha=alpha, beta=beta)

def sort_vertices(vertices):
    """对顶点进行排序，确保对应关系正确"""
    center_x = sum([p[0][0] for p in vertices]) / 4
    center_y = sum([p[0][1] for p in vertices]) / 4
    
    def angle_from_center(point):
        return math.atan2(point[0][1] - center_y, point[0][0] - center_x)
    
    return sorted(vertices, key=angle_from_center)

def detect_rectangles(frame):
    """检测矩形框并返回顶点坐标，同时在图像上绘制标注"""
    # CPU版本图像处理
    scale_percent = 50
    width = int(frame.shape[1] * scale_percent / 100)
    height = int(frame.shape[0] * scale_percent / 100)
    resized_image = cv2.resize(frame, (width, height), interpolation=cv2.INTER_AREA)

    # 转换为灰度并二值化
    gray_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2GRAY)
    _, binary_image = cv2.threshold(gray_image, 46, 255, cv2.THRESH_BINARY)

    # 查找轮廓
    contours, _ = cv2.findContours(binary_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    approximated_contours = []
    for contour in contours:
        area = cv2.contourArea(contour)
        if MIN_CONTOUR_AREA < area < MAX_CONTOUR_AREA:
            epsilon = 0.03 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            if len(approx) == TARGET_SIDES:
                approximated_contours.append((approx, area))

    # 按面积排序
    approximated_contours.sort(key=lambda x: x[1], reverse=True)

    # 计算并打印矩形坐标，同时在图像上绘制
    if len(approximated_contours) >= 2:
        outer_rect = approximated_contours[0][0]  # 外框
        inner_rect = approximated_contours[1][0]  # 内框

        outer_sorted = sort_vertices(outer_rect)
        inner_sorted = sort_vertices(inner_rect)

        if ENABLE_PRINT_COORDS:
            print("=== 矩形框坐标 ===")

        # 准备绘制点
        outer_points = []
        inner_points = []
        mid_points = []

        for i in range(4):
            outer_x, outer_y = outer_sorted[i][0]
            inner_x, inner_y = inner_sorted[i][0]

            # 转换回原始尺寸坐标
            outer_x = int(outer_x * 2)  # 恢复缩放
            outer_y = int(outer_y * 2)
            inner_x = int(inner_x * 2)
            inner_y = int(inner_y * 2)

            mid_x = (outer_x + inner_x) // 2
            mid_y = (outer_y + inner_y) // 2

            outer_points.append((outer_x, outer_y))
            inner_points.append((inner_x, inner_y))
            mid_points.append((mid_x, mid_y))

            #print(f"外框点{i+1}: ({outer_x}, {outer_y})")
            #print(f"内框点{i+1}: ({inner_x}, {inner_y})")
            if ENABLE_PRINT_COORDS:
                print(f"中点{i+1}: ({mid_x}, {mid_y})")

        # 发送矩形中点坐标到串口
        send_rect_midpoints(mid_points)

        # 在图像上绘制中点标注（仅在启用显示时）
        if ENABLE_DISPLAY:
            for i, mid_pt in enumerate(mid_points):
                # 中点 - 黄色圆圈
                cv2.circle(frame, mid_pt, 5, (0, 255, 255), -1)
                cv2.circle(frame, mid_pt, 8, (0, 255, 255), 2)

            # 添加中点坐标标签
            label = f"M{i+1}({mid_pt[0]},{mid_pt[1]})"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]

            # 计算文字位置，避免超出图像边界
            text_x = mid_pt[0] + 15
            text_y = mid_pt[1] - 15
            if text_x + label_size[0] > frame.shape[1]:
                text_x = mid_pt[0] - label_size[0] - 15
            if text_y < 20:
                text_y = mid_pt[1] + 25

            # 绘制文字背景
            cv2.rectangle(frame, (text_x-2, text_y-15),
                        (text_x + label_size[0] + 2, text_y + 5),
                        (0, 0, 0), -1)

            # 绘制文字
            cv2.putText(frame, label, (text_x, text_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

        # 添加图例
        cv2.putText(frame, "Yellow: Mid Points with Coordinates", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

    return approximated_contours

def detect_laser_points(frame):
    """检测激光点并在图像上绘制标注

    Returns:
        bool: 是否检测到红色激光点
    """
    # 应用亮度和对比度调整
    adjusted_frame = adjust_brightness_contrast(frame, BRIGHTNESS, CONTRAST)

    # 绘制ROI区域框
    if ROI is not None:
        x1, y1, x2, y2 = ROI
        cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 255, 255), 2)
        cv2.putText(frame, "ROI", (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    # 应用ROI
    if ROI is not None:
        x1, y1, x2, y2 = ROI
        height, width = adjusted_frame.shape[:2]

        print(f"🔍 图像尺寸: {width}x{height}")
        print(f"📍 原始ROI: [{x1}, {y1}, {x2}, {y2}]")
        print(f"⚙️ 检测参数: 灰度[{GRAY_MIN}, {GRAY_MAX}], 亮度={BRIGHTNESS}, 对比度={CONTRAST}")

        x1 = max(0, min(x1, width - 1))
        y1 = max(0, min(y1, height - 1))
        x2 = max(0, min(x2, width - 1))
        y2 = max(0, min(y2, height - 1))

        print(f"📍 修正后ROI: [{x1}, {y1}, {x2}, {y2}]")
        print(f"📏 ROI尺寸: {x2-x1}x{y2-y1}")

        if x2 > x1 and y2 > y1:
            roi_frame = adjusted_frame[y1:y2, x1:x2].copy()
            print(f"✅ ROI提取成功，尺寸: {roi_frame.shape}")

            if roi_frame.size > 0:
                # 转换为灰度图并二值化
                gray_roi = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2GRAY)
                binary = cv2.inRange(gray_roi, GRAY_MIN, GRAY_MAX)

                # 调试信息：显示处理过程
                white_pixels = cv2.countNonZero(binary)
                total_pixels = binary.shape[0] * binary.shape[1]
                print(f"🎯 灰度阈值 [{GRAY_MIN}, {GRAY_MAX}]: 白色像素={white_pixels}/{total_pixels} ({white_pixels/total_pixels*100:.1f}%)")

                if INVERT:
                    binary = cv2.bitwise_not(binary)
                    print("🔄 已应用图像反转")

                # 膨胀处理
                kernel = np.ones((DILATE_KERNEL_SIZE, DILATE_KERNEL_SIZE), np.uint8)
                binary = cv2.dilate(binary, kernel, iterations=DILATE_ITERATIONS)
                white_pixels_after = cv2.countNonZero(binary)
                print(f"🔧 膨胀处理 (核大小={DILATE_KERNEL_SIZE}, 迭代={DILATE_ITERATIONS}): 白色像素={white_pixels_after}")

                # 寻找轮廓
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                print(f"🔍 找到 {len(contours)} 个轮廓")

                if ENABLE_PRINT_COORDS:
                    print("=== 激光点坐标 ===")
                laser_count = 0
                red_laser_points = []  # 存储红色激光点坐标

                for i, contour in enumerate(contours):
                    area = cv2.contourArea(contour)
                    print(f"  轮廓 {i+1}: 面积={area:.1f}")

                    if area > 1:
                        x, y, w, h = cv2.boundingRect(contour)
                        print(f"    边界框: x={x}, y={y}, w={w}, h={h}")

                        # 分析激光颜色
                        mask = np.zeros(roi_frame.shape[:2], dtype=np.uint8)
                        cv2.drawContours(mask, [contour], 0, 255, -1)
                        masked_image = cv2.bitwise_and(roi_frame, roi_frame, mask=mask)

                        non_zero_pixels = cv2.countNonZero(mask)
                        print(f"    轮廓内像素数: {non_zero_pixels}")

                        if non_zero_pixels > 0:
                            b_sum = int(np.sum(masked_image[:, :, 0]))
                            g_sum = int(np.sum(masked_image[:, :, 1]))
                            r_sum = int(np.sum(masked_image[:, :, 2]))
                            print(f"    颜色分析: R={r_sum}, G={g_sum}, B={b_sum}")

                            # 只检测红色激光点
                            if r_sum > g_sum * 1.0:
                                laser_type = "RED"
                                color = (0, 0, 255)  # 红色
                                print(f"    ✅ 识别为红色激光")

                                center_x = x + w // 2
                                center_y = y + h // 2
                                abs_x = x1 + center_x
                                abs_y = y1 + center_y
                                print(f"    ROI内中心: ({center_x}, {center_y})")
                                print(f"    全局坐标: ({abs_x}, {abs_y})")

                                if ENABLE_PRINT_COORDS:
                                    print(f"{laser_type}激光点: ({abs_x}, {abs_y})")

                                # 收集红色激光点坐标
                                red_laser_points.append((abs_x, abs_y))

                                # 在原图上绘制激光点标注
                                # 绘制圆圈标记激光点
                                cv2.circle(frame, (abs_x, abs_y), 8, color, 2)
                                cv2.circle(frame, (abs_x, abs_y), 3, color, -1)

                                # 绘制十字标记
                                cv2.line(frame, (abs_x-10, abs_y), (abs_x+10, abs_y), color, 2)
                                cv2.line(frame, (abs_x, abs_y-10), (abs_x, abs_y+10), color, 2)

                                # 添加文字标签
                                label = f"{laser_type}({abs_x},{abs_y})"
                                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]

                                # 计算文字位置，避免超出图像边界
                                text_x = abs_x + 15
                                text_y = abs_y - 15
                                if text_x + label_size[0] > frame.shape[1]:
                                    text_x = abs_x - label_size[0] - 15
                                if text_y < 20:
                                    text_y = abs_y + 25

                                # 绘制文字背景
                                cv2.rectangle(frame, (text_x-2, text_y-15),
                                            (text_x + label_size[0] + 2, text_y + 5),
                                            (0, 0, 0), -1)

                                # 绘制文字
                                cv2.putText(frame, label, (text_x, text_y),
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

                                laser_count += 1
                            else:
                                print(f"    ❌ 不是红色激光 (R/G比值: {r_sum/max(g_sum,1):.2f})")
                    else:
                        print(f"    ❌ 轮廓面积太小，忽略")

                print(f"🎯 总共检测到 {laser_count} 个有效激光点")
                print(f"🔴 红色激光点数量: {len(red_laser_points)}")

                # 发送红色激光点坐标到串口（只发送第一个红色激光点）
                if red_laser_points:
                    send_red_laser_point(red_laser_points[0])
                    print(f"📡 发送红色激光点坐标: {red_laser_points[0]}")
                    if len(red_laser_points) > 1:
                        print(f"检测到{len(red_laser_points)}个红色激光点，只发送第一个")
                else:
                    print("❌ 未检测到红色激光点")

                # 在图像上显示红色激光点统计信息（仅在启用显示时）
                if ENABLE_DISPLAY:
                    cv2.putText(frame, f"Red Laser Points: {laser_count}", (10, 110),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                # 返回是否检测到红色激光点
                return len(red_laser_points) > 0
            else:
                print("❌ ROI区域为空")
                return False
        else:
            print("❌ ROI区域无效")
            return False
    else:
        print("❌ ROI未设置")
        return False

# 加载参数
load_parameters()

# 初始化串口
init_serial()

# 初始化时间戳
last_rect_send_time = time.time()

# 初始化性能监控变量
frame_count = 0
fps_start_time = time.time()
last_temp_check = 0

# 打开摄像头
cap = cv2.VideoCapture(0)

print("按ESC退出程序")
print("程序将同时检测矩形框和红色激光点坐标")
print("图像标注说明:")
print("- 黄色圆圈: 矩形框中点坐标 (M1-M4)")
print("- 红色圆圈: 红色激光点坐标")
print("- 白色矩形: ROI检测区域")
if ENABLE_SERIAL and serial_connection:
    print("- 串口发送: 矩形中点坐标(500ms间隔) + 红色激光点坐标(33.3Hz每帧)")
else:
    print("- 串口发送: 已禁用或连接失败")
if ENABLE_DISPLAY:
    print("- 图像显示: 已启用")
else:
    print("- 图像显示: 已禁用")

while True:
    frame_start_time = time.time()  # 记录帧开始时间

    ret, frame = cap.read()
    if not ret:
        print("无法从摄像头读取")
        break

    if frame is None or frame.size == 0:
        continue

    # 先检测激光点
    laser_detected = detect_laser_points(frame)

    # 只有在没有检测到红色激光点时才检测矩形框
    if not laser_detected and ENABLE_RECT_DETECTION:
        detect_rectangles(frame)

    # Jetson性能监控
    frame_count += 1
    frame_end_time = time.time()
    frame_process_time = (frame_end_time - frame_start_time) * 1000  # 转换为毫秒

    # 每100帧显示一次性能统计
    if frame_count % 100 == 0:
        elapsed_time = frame_end_time - fps_start_time
        fps = frame_count / elapsed_time
        print(f"CPU性能: {fps:.1f} FPS, 平均帧时间: {elapsed_time*1000/frame_count:.1f}ms")

        # 重置计数器
        frame_count = 0
        fps_start_time = frame_end_time

    # 每5秒检查一次温度
    if frame_end_time - last_temp_check > 5:
        try:
            with open('/sys/class/thermal/thermal_zone0/temp', 'r') as f:
                temp = int(f.read().strip()) / 1000
                if temp > 70:  # 温度过高警告
                    print(f"⚠️ 设备温度过高: {temp:.1f}°C")
                elif frame_count == 0:  # 只在性能统计时显示正常温度
                    print(f"🌡️ 设备温度: {temp:.1f}°C")
        except:
            pass
        last_temp_check = frame_end_time

    # 帧处理时间警告
    if frame_process_time > 30:  # CPU模式目标30ms
        print(f"⚠️ 帧处理时间: {frame_process_time:.1f}ms (目标<30ms)")

    if ENABLE_PRINT_COORDS:
        print("-" * 40)  # 分隔线

    # 显示带标注的图像（根据标志位控制）
    if ENABLE_DISPLAY:
        cv2.imshow("Rectangle & Laser Detection with Annotations", frame)

    # 按ESC退出
    if cv2.waitKey(5) == 27:  # 改为5ms，提高到200Hz
        break

cap.release()
if ENABLE_DISPLAY:
    cv2.destroyAllWindows()

# 关闭串口连接
close_serial()