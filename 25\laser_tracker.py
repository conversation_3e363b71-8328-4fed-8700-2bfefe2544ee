#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能激光轨迹跟踪器
融合gray_processor的红色激光检测功能和轨迹跟踪控制
实时检测红色激光点位置，并进行轨迹跟踪控制
"""

import cv2
import numpy as np
import time
import json
import os
import glob

class LaserTracker:
    def __init__(self):
        # 轨迹数据
        self.trajectory_points = []
        self.current_index = 0
        self.total_points = 0
        
        # 控制参数
        self.distance_threshold = 3.0  # 距离阈值（像素）
        self.max_time_per_point = 200  # 最大停留时间（ms）
        self.min_time_per_point = 50   # 最小停留时间（ms）
        self.control_cycle = 30        # 控制周期（ms）
        
        # 状态变量
        self.current_position = [0.0, 0.0]  # 当前激光位置
        self.target_position = [0.0, 0.0]   # 目标位置
        self.is_tracking = False
        self.start_time = 0
        self.last_switch_time = 0
        
        print("🚀 激光轨迹跟踪器已启动")
        print(f"控制周期: {self.control_cycle}ms")
        print(f"距离阈值: {self.distance_threshold}px")
        print(f"时间范围: {self.min_time_per_point}-{self.max_time_per_point}ms")
    
    def load_coordinate_file(self, filename=None):
        """加载坐标文件"""
        if filename is None:
            # 自动查找最新的坐标文件
            files = glob.glob("laser_coordinates_*.txt")
            if not files:
                print("❌ 未找到坐标文件")
                return False
            filename = max(files, key=os.path.getctime)
            print(f"📁 自动选择最新文件: {filename}")
        
        try:
            self.trajectory_points = []
            with open(filename, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('#') or not line:
                        continue
                    
                    x, y = map(float, line.split(','))
                    self.trajectory_points.append([x, y])
            
            self.total_points = len(self.trajectory_points)
            print(f"✅ 成功加载 {self.total_points} 个轨迹点")
            
            if self.total_points > 0:
                print(f"起点: ({self.trajectory_points[0][0]:.1f}, {self.trajectory_points[0][1]:.1f})")
                print(f"终点: ({self.trajectory_points[-1][0]:.1f}, {self.trajectory_points[-1][1]:.1f})")
                print(f"预计时间: {self.total_points * self.control_cycle / 1000:.1f}秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return False
    
    def start_tracking(self, start_position=None):
        """开始轨迹跟踪"""
        if not self.trajectory_points:
            print("❌ 没有轨迹数据")
            return False
        
        self.current_index = 0
        self.is_tracking = True
        self.start_time = time.time() * 1000
        self.last_switch_time = self.start_time
        
        # 设置起始位置
        if start_position:
            self.current_position = list(start_position)
        else:
            self.current_position = self.trajectory_points[0].copy()
        
        # 设置第一个目标
        self.target_position = self.trajectory_points[0].copy()
        
        print("\n🎯 开始轨迹跟踪")
        print(f"起始位置: ({self.current_position[0]:.1f}, {self.current_position[1]:.1f})")
        print(f"目标位置: ({self.target_position[0]:.1f}, {self.target_position[1]:.1f})")
        print("=" * 50)
        
        return True
    
    def update_laser_position(self, position):
        """更新激光当前位置（从传感器读取）"""
        self.current_position = list(position)
    
    def calculate_distance(self, pos1, pos2):
        """计算两点间距离"""
        return np.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
    
    def should_switch_target(self):
        """判断是否切换到下一个目标点"""
        if not self.is_tracking:
            return False
        
        current_time = time.time() * 1000
        elapsed_time = current_time - self.last_switch_time
        
        # 计算距离
        distance = self.calculate_distance(self.current_position, self.target_position)
        
        # 判断条件
        distance_ok = distance < self.distance_threshold
        min_time_ok = elapsed_time > self.min_time_per_point
        max_time_exceeded = elapsed_time > self.max_time_per_point
        
        return (distance_ok and min_time_ok) or max_time_exceeded
    
    def switch_to_next_target(self):
        """切换到下一个目标点"""
        if self.current_index >= len(self.trajectory_points) - 1:
            # 轨迹完成
            self.is_tracking = False
            total_time = (time.time() * 1000 - self.start_time) / 1000
            print(f"\n🎉 轨迹跟踪完成！")
            print(f"总用时: {total_time:.1f}秒")
            print(f"平均速度: {self.total_points / total_time:.1f}点/秒")
            return False
        
        # 切换到下一个目标
        self.current_index += 1
        self.target_position = self.trajectory_points[self.current_index].copy()
        self.last_switch_time = time.time() * 1000
        
        progress = (self.current_index / self.total_points) * 100
        print(f"📍 目标 {self.current_index + 1}/{self.total_points} "
              f"({progress:.1f}%) -> ({self.target_position[0]:.1f}, {self.target_position[1]:.1f})")
        
        return True
    
    def get_control_command(self):
        """获取30ms周期的控制命令"""
        if not self.is_tracking:
            return None
        
        # 检查是否需要切换目标
        if self.should_switch_target():
            if not self.switch_to_next_target():
                return None  # 跟踪完成
        
        # 生成控制命令
        distance = self.calculate_distance(self.current_position, self.target_position)
        
        command = {
            "current_position": {
                "x": round(self.current_position[0], 2),
                "y": round(self.current_position[1], 2)
            },
            "target_position": {
                "x": round(self.target_position[0], 2),
                "y": round(self.target_position[1], 2)
            },
            "target_index": self.current_index,
            "progress_percent": round((self.current_index / self.total_points) * 100, 1),
            "distance_to_target": round(distance, 2),
            "timestamp": int(time.time() * 1000)
        }
        
        return command
    
    def stop_tracking(self):
        """停止跟踪"""
        self.is_tracking = False
        print("⏹️ 轨迹跟踪已停止")
    
    def run_simulation(self):
        """运行模拟（用于测试）"""
        if not self.start_tracking():
            return
        
        print("🔄 开始30ms控制循环模拟...")
        cycle_count = 0
        
        while self.is_tracking and cycle_count < 1000:  # 最多1000个周期
            # 模拟激光位置更新（实际应从传感器读取）
            if self.target_position:
                # 模拟激光向目标移动
                dx = self.target_position[0] - self.current_position[0]
                dy = self.target_position[1] - self.current_position[1]
                
                # 模拟移动速度（每周期移动10%的距离）
                self.current_position[0] += dx * 0.1
                self.current_position[1] += dy * 0.1
            
            # 获取控制命令
            command = self.get_control_command()
            if command is None:
                break
            
            # 每10个周期打印一次状态
            if cycle_count % 10 == 0:
                print(f"周期 {cycle_count:3d}: "
                      f"当前({command['current_position']['x']:6.1f}, {command['current_position']['y']:6.1f}) -> "
                      f"目标({command['target_position']['x']:6.1f}, {command['target_position']['y']:6.1f}) "
                      f"距离: {command['distance_to_target']:5.1f}px "
                      f"进度: {command['progress_percent']:5.1f}%")
            
            # 模拟30ms延时
            time.sleep(self.control_cycle / 1000.0)
            cycle_count += 1
        
        print(f"\n模拟完成，总周期数: {cycle_count}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 激光轨迹跟踪器")
    print("=" * 60)
    
    # 创建跟踪器
    tracker = LaserTracker()
    
    # 加载坐标文件
    if not tracker.load_coordinate_file():
        print("\n💡 使用方法:")
        print("1. 先运行 111 (1).py 识别图形")
        print("2. 按 'e' 键导出坐标文件")
        print("3. 再运行此程序进行激光跟踪")
        return
    
    print("\n📋 操作选项:")
    print("1. 按 Enter 开始模拟跟踪")
    print("2. 按 'q' 退出程序")
    
    while True:
        try:
            user_input = input("\n请选择操作: ").strip().lower()
            
            if user_input == 'q':
                print("👋 程序退出")
                break
            elif user_input == '' or user_input == '1':
                # 开始模拟
                tracker.run_simulation()
                print("\n模拟完成，可以重新开始或退出")
            else:
                print("❌ 无效输入，请按 Enter 开始或 'q' 退出")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
