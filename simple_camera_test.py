#!/usr/bin/env python3
import cv2
import numpy as np

print("简单摄像头测试")
print("尝试不同的摄像头...")

# 尝试多个摄像头索引和后端
camera_configs = [
    (0, cv2.CAP_V4L2),
    (1, cv2.CAP_V4L2), 
    (0, cv2.CAP_ANY),
    (1, cv2.CAP_ANY),
    ('/dev/video0', cv2.CAP_V4L2),
    ('/dev/video1', cv2.CAP_V4L2),
]

cap = None
for cam_id, backend in camera_configs:
    print(f"尝试摄像头: {cam_id}, 后端: {backend}")
    try:
        cap = cv2.VideoCapture(cam_id, backend)
        if cap.isOpened():
            # 测试读取一帧
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f"成功! 摄像头 {cam_id} 工作正常")
                print(f"图像尺寸: {frame.shape}")
                break
            else:
                print(f"摄像头 {cam_id} 打开但无法读取")
                cap.release()
        else:
            print(f"无法打开摄像头 {cam_id}")
    except Exception as e:
        print(f"错误: {e}")

if cap is None or not cap.isOpened():
    print("所有摄像头都无法工作!")
    print("请检查:")
    print("1. 摄像头连接")
    print("2. 权限: sudo usermod -a -G video $USER")
    print("3. 驱动: sudo apt install v4l-utils")
    exit()

print("开始显示摄像头画面...")
print("按 'q' 退出")

while True:
    ret, frame = cap.read()
    if not ret:
        print("无法读取帧")
        break
    
    # 显示原始图像
    cv2.imshow('原始摄像头', frame)
    
    # 创建一个测试图像确保显示正常
    test_img = frame.copy()
    cv2.rectangle(test_img, (50, 50), (200, 150), (0, 255, 0), 3)
    cv2.putText(test_img, "Test OK", (60, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    cv2.imshow('测试图像', test_img)
    
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
print("测试完成")
