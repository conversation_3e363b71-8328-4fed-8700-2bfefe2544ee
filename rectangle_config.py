#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矩形检测器配置文件
统一管理所有检测参数，支持不同场景的预设配置
"""

import numpy as np

# =========================== 基础配置 ===========================

# A4纸检测配置
A4_PAPER_CONFIG = {
    # HSV白色检测范围
    'lower_white_hsv': np.array([0, 0, 0]),      # 白色HSV下限
    'upper_white_hsv': np.array([250, 20, 250]), # 白色HSV上限
    
    # 面积筛选参数
    'min_area': 50,        # 最小面积
    'max_area': 100000,    # 最大面积
    
    # 长宽比参数 (A4纸标准比例)
    'target_aspect_ratio': 1.414,  # 297mm/210mm
    'aspect_tolerance': 0.2,       # ±20%容差
    
    # 形态学操作参数
    'do_morphology': True,         # 启用形态学操作
    'kernel_size': 3,              # 3x3核
    'morph_iterations': 1,         # 迭代1次
    
    # 多边形逼近参数
    'approx_epsilon_ratio': 0.03,  # 逼近精度3%
    
    # 显示参数
    'draw_contour': True,          # 绘制轮廓
    'draw_center': True,           # 绘制中心点
    'contour_color': (0, 255, 0),  # 绿色轮廓
    'center_color': (0, 0, 255),   # 红色中心点
    'text_color': (255, 255, 255), # 白色文字
}

# 通用矩形检测配置
GENERAL_RECTANGLE_CONFIG = {
    # HSV白色检测范围 (更宽松)
    'lower_white_hsv': np.array([0, 0, 100]),    # 提高V下限
    'upper_white_hsv': np.array([180, 30, 255]), # 放宽H和S范围
    
    # 面积筛选参数
    'min_area': 100,       # 稍大的最小面积
    'max_area': 200000,    # 更大的最大面积
    
    # 长宽比参数 (更宽松)
    'target_aspect_ratio': 1.5,   # 通用比例
    'aspect_tolerance': 0.5,      # ±50%容差
    
    # 形态学操作参数
    'do_morphology': True,
    'kernel_size': 5,             # 更大的核
    'morph_iterations': 2,        # 更多迭代
    
    # 多边形逼近参数
    'approx_epsilon_ratio': 0.02, # 更精确的逼近
    
    # 显示参数
    'draw_contour': True,
    'draw_center': True,
    'contour_color': (255, 0, 0),  # 蓝色轮廓
    'center_color': (0, 255, 0),   # 绿色中心点
    'text_color': (255, 255, 0),   # 黄色文字
}

# 小矩形检测配置 (适用于小物体)
SMALL_RECTANGLE_CONFIG = {
    # HSV检测范围
    'lower_white_hsv': np.array([0, 0, 150]),    # 更高的亮度要求
    'upper_white_hsv': np.array([180, 50, 255]),
    
    # 面积筛选参数 (小物体)
    'min_area': 20,        # 很小的最小面积
    'max_area': 5000,      # 较小的最大面积
    
    # 长宽比参数
    'target_aspect_ratio': 1.0,   # 正方形
    'aspect_tolerance': 0.8,      # 很宽松的容差
    
    # 形态学操作参数 (轻微处理)
    'do_morphology': True,
    'kernel_size': 2,             # 小核
    'morph_iterations': 1,
    
    # 多边形逼近参数
    'approx_epsilon_ratio': 0.04, # 稍微宽松的逼近
    
    # 显示参数
    'draw_contour': True,
    'draw_center': True,
    'contour_color': (0, 255, 255), # 青色轮廓
    'center_color': (255, 0, 255),  # 紫色中心点
    'text_color': (0, 0, 255),      # 红色文字
}

# 大矩形检测配置 (适用于大物体)
LARGE_RECTANGLE_CONFIG = {
    # HSV检测范围
    'lower_white_hsv': np.array([0, 0, 80]),     # 较低的亮度要求
    'upper_white_hsv': np.array([180, 40, 255]),
    
    # 面积筛选参数 (大物体)
    'min_area': 5000,      # 大的最小面积
    'max_area': 500000,    # 很大的最大面积
    
    # 长宽比参数
    'target_aspect_ratio': 1.6,   # 稍长的矩形
    'aspect_tolerance': 0.3,      # 适中的容差
    
    # 形态学操作参数 (强处理)
    'do_morphology': True,
    'kernel_size': 7,             # 大核
    'morph_iterations': 3,        # 多次迭代
    
    # 多边形逼近参数
    'approx_epsilon_ratio': 0.01, # 很精确的逼近
    
    # 显示参数
    'draw_contour': True,
    'draw_center': True,
    'contour_color': (128, 0, 128), # 紫色轮廓
    'center_color': (255, 128, 0),  # 橙色中心点
    'text_color': (0, 255, 128),    # 绿青色文字
}

# 高精度检测配置 (严格检测)
HIGH_PRECISION_CONFIG = {
    # HSV检测范围 (严格)
    'lower_white_hsv': np.array([0, 0, 200]),    # 很高的亮度要求
    'upper_white_hsv': np.array([180, 15, 255]), # 很低的饱和度要求
    
    # 面积筛选参数
    'min_area': 500,       # 适中的最小面积
    'max_area': 50000,     # 适中的最大面积
    
    # 长宽比参数 (严格)
    'target_aspect_ratio': 1.414, # A4标准比例
    'aspect_tolerance': 0.1,      # ±10%严格容差
    
    # 形态学操作参数 (精细处理)
    'do_morphology': True,
    'kernel_size': 3,
    'morph_iterations': 1,
    
    # 多边形逼近参数 (高精度)
    'approx_epsilon_ratio': 0.005, # 很精确的逼近
    
    # 显示参数
    'draw_contour': True,
    'draw_center': True,
    'contour_color': (0, 128, 255), # 橙蓝色轮廓
    'center_color': (255, 0, 128),  # 粉红色中心点
    'text_color': (128, 255, 128),  # 浅绿色文字
}

# =========================== 配置管理器 ===========================

class RectangleConfigManager:
    """矩形检测配置管理器"""
    
    # 预设配置字典
    PRESETS = {
        'a4_paper': A4_PAPER_CONFIG,
        'general': GENERAL_RECTANGLE_CONFIG,
        'small': SMALL_RECTANGLE_CONFIG,
        'large': LARGE_RECTANGLE_CONFIG,
        'high_precision': HIGH_PRECISION_CONFIG,
    }
    
    @classmethod
    def get_config(cls, preset_name='a4_paper'):
        """获取预设配置
        Args:
            preset_name (str): 预设名称
        Returns:
            dict: 配置字典的副本
        """
        if preset_name not in cls.PRESETS:
            print(f"警告: 未知的预设名称 '{preset_name}'，使用默认配置 'a4_paper'")
            preset_name = 'a4_paper'
        
        return cls.PRESETS[preset_name].copy()
    
    @classmethod
    def list_presets(cls):
        """列出所有可用的预设配置"""
        return list(cls.PRESETS.keys())
    
    @classmethod
    def create_custom_config(cls, base_preset='a4_paper', **kwargs):
        """基于预设创建自定义配置
        Args:
            base_preset (str): 基础预设名称
            **kwargs: 要覆盖的配置参数
        Returns:
            dict: 自定义配置字典
        """
        config = cls.get_config(base_preset)
        config.update(kwargs)
        return config

# =========================== 使用示例 ===========================

if __name__ == "__main__":
    # 创建配置管理器
    config_manager = RectangleConfigManager()
    
    print("=== 矩形检测配置管理器 ===\n")
    
    # 列出所有预设
    print("可用的预设配置:")
    for preset in config_manager.list_presets():
        print(f"  - {preset}")
    
    print("\n" + "="*50)
    
    # 获取A4纸配置
    a4_config = config_manager.get_config('a4_paper')
    print("\nA4纸检测配置:")
    for key, value in a4_config.items():
        print(f"  {key}: {value}")
    
    print("\n" + "="*50)
    
    # 创建自定义配置
    custom_config = config_manager.create_custom_config(
        'general',
        min_area=200,
        contour_color=(255, 255, 0),  # 黄色轮廓
        target_aspect_ratio=2.0
    )
    print("\n自定义配置示例:")
    for key, value in custom_config.items():
        print(f"  {key}: {value}")
