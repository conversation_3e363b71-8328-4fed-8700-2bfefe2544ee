import cv2
import numpy as np
import time

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)  # 形态学处理核
        
    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        # 紫色HSV范围检测（可根据实际调整）
        lower_purple = np.array([130, 80, 80])
        upper_purple = np.array([160, 255, 255])
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []
        
        for cnt in contours_purple:
            # 计算激光点中心
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            laser_points.append((cx, cy))
            # 绘制激光点标记
            cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        return img, laser_points

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    print("启动Jetson Nano矩形和激光检测程序...")

    # 初始化摄像头
    cap = cv2.VideoCapture(0)  # 使用默认摄像头
    if not cap.isOpened():
        print("无法打开摄像头")
        exit()

    # 设置摄像头分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)

    # 初始化激光检测器
    laser_detector = PurpleLaserDetector()

    # 定义参数
    min_contour_area = 3000
    max_contour_area = 40000
    target_sides = 4

    print("按 'q' 退出程序")

    while True:
        # 从摄像头读取图像
        ret, img_cv = cap.read()
        if not ret:
            print("无法读取摄像头画面")
            break
        output = img_cv.copy()

        # 1. 矩形检测与处理
        # 转灰度并二值化
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 按面积排序（外框在前，内框在后）
        quads.sort(key=lambda x: x[1], reverse=True)
        
        # 移除最大的外框（只保留内框）
        inner_quads = quads[1:]
        
        # 处理内框
        inner_centers = []
        for approx, area in inner_quads:
            # 计算并绘制中心点
            M = cv2.moments(approx)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                inner_centers.append((cx, cy))
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)  # 蓝色中心点
                # 绘制内框轮廓（可选）
                cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

        # 2. 激光检测
        output, laser_points = laser_detector.detect(output)

        # 3. 显示检测结果信息
        # 在图像上显示检测到的内框数量和激光点数量
        info_text = f"Inner Centers: {len(inner_centers)}, Laser Points: {len(laser_points)}"
        cv2.putText(output, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # 显示图像
        cv2.imshow('矩形和激光检测', output)

        # 按键检测
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break

    # 清理资源
    cap.release()
    cv2.destroyAllWindows()
    print("程序已退出")