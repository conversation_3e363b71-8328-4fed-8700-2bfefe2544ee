import cv2
import numpy as np

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)  # 形态学处理核
        
    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        # 紫色HSV范围检测（可根据实际调整）
        lower_purple = np.array([130, 80, 80])
        upper_purple = np.array([160, 255, 255])
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []
        
        for cnt in contours_purple:
            # 计算激光点中心
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            laser_points.append((cx, cy))
            # 绘制激光点标记
            cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        return img, laser_points

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    print("启动Jetson Nano矩形和激光检测程序...")

    # 初始化摄像头 - 尝试多种方式
    print("正在初始化摄像头...")
    cap = None

    # 尝试不同的摄像头配置
    camera_configs = [
        (0, cv2.CAP_V4L2),
        (1, cv2.CAP_V4L2),
        (0, cv2.CAP_ANY),
        (1, cv2.CAP_ANY),
    ]

    for cam_id, backend in camera_configs:
        print(f"尝试摄像头 {cam_id} (后端: {backend})")
        try:
            cap = cv2.VideoCapture(cam_id, backend)
            if cap.isOpened():
                # 测试读取
                ret, test_frame = cap.read()
                if ret and test_frame is not None:
                    print(f"摄像头 {cam_id} 初始化成功!")
                    print(f"测试帧尺寸: {test_frame.shape}")
                    break
                else:
                    print(f"摄像头 {cam_id} 无法读取帧")
                    cap.release()
                    cap = None
        except Exception as e:
            print(f"摄像头 {cam_id} 错误: {e}")

    if cap is None:
        print("无法初始化任何摄像头!")
        exit()

    # 设置摄像头分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)

    # 获取实际分辨率
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    actual_fps = int(cap.get(cv2.CAP_PROP_FPS))
    print(f"摄像头分辨率: {actual_width}x{actual_height}, FPS: {actual_fps}")

    # 初始化激光检测器
    laser_detector = PurpleLaserDetector()

    # 定义参数
    min_contour_area = 3000
    max_contour_area = 40000
    target_sides = 4

    print("按键说明:")
    print("  'q' - 退出程序")
    print("  't' - 显示测试图案")
    print("开始摄像头循环...")

    frame_count = 0
    while True:
        # 从摄像头读取图像
        ret, img_cv = cap.read()
        if not ret:
            print("无法读取摄像头画面")
            break

        frame_count += 1
        if frame_count % 30 == 0:  # 每30帧打印一次
            print(f"已处理 {frame_count} 帧，图像尺寸: {img_cv.shape}")

        output = img_cv.copy()

        # 1. 矩形检测与处理
        # 转灰度并二值化
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 按面积排序（外框在前，内框在后）
        quads.sort(key=lambda x: x[1], reverse=True)
        
        # 移除最大的外框（只保留内框）
        inner_quads = quads[1:]
        
        # 处理内框
        inner_centers = []
        for approx, area in inner_quads:
            # 计算并绘制中心点
            M = cv2.moments(approx)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                inner_centers.append((cx, cy))
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)  # 蓝色中心点
                # 绘制内框轮廓（可选）
                cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

        # 2. 激光检测
        output, laser_points = laser_detector.detect(output)

        # 3. 显示检测结果信息
        # 在图像上显示检测到的内框数量和激光点数量
        info_text = f"Inner Centers: {len(inner_centers)}, Laser Points: {len(laser_points)}"
        cv2.putText(output, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # 检查图像是否为空或全黑
        if output is None:
            print("警告: 图像为空")
            continue

        # 检查图像亮度
        gray_check = cv2.cvtColor(output, cv2.COLOR_BGR2GRAY)
        mean_brightness = np.mean(gray_check)

        if frame_count % 30 == 0:  # 每30帧检查一次
            print(f"图像平均亮度: {mean_brightness:.2f}")
            if mean_brightness < 5:
                print("警告: 图像过暗，可能是摄像头问题")

        # 如果图像太暗，添加一些测试图案
        if mean_brightness < 5:
            # 绘制测试图案
            cv2.rectangle(output, (50, 50), (200, 150), (0, 255, 0), 2)
            cv2.putText(output, "Camera Test", (60, 100),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        # 显示图像
        cv2.imshow('矩形和激光检测', output)

        # 按键检测
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('t'):  # 按't'显示测试图案
            test_img = np.zeros((480, 640, 3), dtype=np.uint8)
            cv2.rectangle(test_img, (100, 100), (500, 300), (0, 255, 0), 3)
            cv2.putText(test_img, "Test Pattern", (200, 220),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.imshow('矩形和激光检测', test_img)
            cv2.waitKey(1000)  # 显示1秒

    # 清理资源
    cap.release()
    cv2.destroyAllWindows()
    print("程序已退出")