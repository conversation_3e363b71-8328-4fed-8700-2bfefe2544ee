import serial
import struct
import time

class SerialComm:
    """串口通信类 - 分离自23E.py"""
    
    def __init__(self, port='/dev/ttyTHS0', baudrate=115200, enable=True):
        self.port = port
        self.baudrate = baudrate
        self.enable = enable
        self.connection = None
        self.last_rect_send_time = 0
        self.rect_send_interval = 0.5  # 矩形发送间隔500ms
        
        # 指令类型定义
        self.CMD_RECT_VERTICES = 0x01  # 方框顶点坐标
        self.CMD_LASER_POINT = 0x02    # 激光点坐标
        
    def init(self):
        """初始化串口连接"""
        if not self.enable:
            print("串口发送功能已禁用")
            return None
            
        try:
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"串口 {self.port} 已打开，波特率 {self.baudrate}")
            return self.connection
        except serial.SerialException as e:
            print(f"无法打开串口 {self.port}: {e}")
            self.connection = None
            return None
    
    def create_packet(self, cmd, data):
        """创建串口通信数据包
        
        数据包格式：[帧头2字节][指令1字节][数据长度1字节][数据N字节][校验和1字节]
        """
        header = bytes([0xAA, 0x55])  # 帧头
        cmd_byte = bytes([cmd])       # 指令类型
        data_len = len(data)
        len_byte = bytes([data_len])  # 数据长度
        
        # 校验和计算
        checksum = cmd + data_len
        for byte in data:
            checksum += byte
        checksum = checksum & 0xFF
        checksum_byte = bytes([checksum])
        
        return header + cmd_byte + len_byte + data + checksum_byte
    
    def send_rect_midpoints(self, midpoints):
        """发送矩形框四个中点坐标（带频率控制）"""
        if self.connection is None or not self.enable or midpoints is None:
            return
            
        current_time = time.time()
        if current_time - self.last_rect_send_time < self.rect_send_interval:
            return  # 还未到发送时间
            
        if len(midpoints) != 4:
            print(f"警告: 矩形中点数量不正确，期望4个，实际{len(midpoints)}个")
            return
            
        try:
            data = bytes()
            for (x, y) in midpoints:
                x = max(0, int(x))
                y = max(0, int(y))
                data += struct.pack('<HH', x, y)  # 小端字节序
                
            packet = self.create_packet(self.CMD_RECT_VERTICES, data)
            self.connection.write(packet)
            self.last_rect_send_time = current_time
            
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 串口发送矩形中点: {midpoints}")
            
        except Exception as e:
            print(f"发送矩形中点数据时出错: {e}")
    
    def send_red_laser_point(self, point):
        """发送红色激光点坐标（每帧发送，33.3 Hz）"""
        if self.connection is None or not self.enable or point is None:
            return
            
        try:
            x = max(0, int(point[0]))
            y = max(0, int(point[1]))
            data = struct.pack('<HH', x, y)
            
            packet = self.create_packet(self.CMD_LASER_POINT, data)
            self.connection.write(packet)
            
            current_time = time.time()
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 串口发送红色激光点: ({x}, {y})")
            
        except Exception as e:
            print(f"发送激光点数据时出错: {e}")

    def send_custom_commands(self):
        """发送自定义指令序列"""
        if not self.enable or not self.connection:
            return False

        try:
            import time

            # 第一个指令: 0x01,0xF3,0xAB,0x01,0x00,0x6B
            cmd1 = bytes([0x01, 0xF3, 0xAB, 0x01, 0x00, 0x6B])
            self.connection.write(cmd1)
            current_time = time.time()
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 发送指令1: {' '.join([f'0x{b:02X}' for b in cmd1])}")

            # 等待10ms
            time.sleep(0.01)

            # 第二个指令: 0x01,0xF3,0xAB,0x01,0x00,0x6B (重复)
            cmd2 = bytes([0x01, 0xF3, 0xAB, 0x01, 0x00, 0x6B])
            self.connection.write(cmd2)
            current_time = time.time()
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 发送指令2: {' '.join([f'0x{b:02X}' for b in cmd2])}")

            # 第三个指令: 0x00,0xFE,0x98,0x00,0x6B
            cmd3 = bytes([0x00, 0xFE, 0x98, 0x00, 0x6B])
            self.connection.write(cmd3)
            current_time = time.time()
            timestamp = time.strftime("%H:%M:%S", time.localtime(current_time))
            milliseconds = int((current_time % 1) * 1000)
            print(f"[{timestamp}.{milliseconds:03d}] 发送指令3: {' '.join([f'0x{b:02X}' for b in cmd3])}")

            return True

        except Exception as e:
            print(f"发送自定义指令失败: {e}")
            return False

    def close(self):
        """关闭串口连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            print("串口已关闭")