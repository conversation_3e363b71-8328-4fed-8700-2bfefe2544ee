#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径规划算法对比展示页面
展示不同算法下的路径规划点数和效果
"""

import cv2
import numpy as np
import time
from scipy.interpolate import splprep, splev

class PathPlanningComparison:
    def __init__(self):
        self.window_width = 1200
        self.window_height = 800
        self.canvas = np.zeros((self.window_height, self.window_width, 3), dtype=np.uint8)
        
        # 示例轨迹数据（模拟从骨架提取的点）
        self.original_points = self.generate_sample_trajectory()
        
        # 不同算法的结果
        self.interpolated_200 = None
        self.douglas_peucker = None
        self.key_points_30 = None
        self.key_points_50 = None
        
        # 计算所有算法结果
        self.calculate_all_algorithms()
        
    def generate_sample_trajectory(self):
        """生成示例轨迹数据"""
        # 创建一个S形曲线作为示例
        t = np.linspace(0, 4*np.pi, 80)
        x = 200 + 150 * np.sin(t) + 50 * np.sin(3*t)
        y = 200 + 100 * np.cos(t/2) + 30 * np.cos(2*t)
        
        # 添加一些噪声模拟真实骨架提取
        x += np.random.normal(0, 2, len(x))
        y += np.random.normal(0, 2, len(y))
        
        return np.column_stack([x, y])
    
    def interpolate_trajectory(self, points, num_points=200):
        """样条插值平滑轨迹"""
        if len(points) < 4:
            return points
        
        try:
            # 样条插值
            tck, u = splprep([points[:, 0], points[:, 1]], s=0, k=min(3, len(points)-1))
            u_new = np.linspace(0, 1, num_points)
            x_new, y_new = splev(u_new, tck)
            return np.column_stack([x_new, y_new])
        except:
            return points
    
    def douglas_peucker_simplify(self, points, epsilon=3.0):
        """Douglas-Peucker算法简化"""
        if len(points) <= 2:
            return points
        
        # 找到距离直线最远的点
        start, end = points[0], points[-1]
        max_dist = 0
        max_index = 0
        
        for i in range(1, len(points) - 1):
            dist = self.point_to_line_distance(points[i], start, end)
            if dist > max_dist:
                max_dist = dist
                max_index = i
        
        # 递归简化
        if max_dist > epsilon:
            left_points = self.douglas_peucker_simplify(points[:max_index+1], epsilon)
            right_points = self.douglas_peucker_simplify(points[max_index:], epsilon)
            return np.vstack([left_points[:-1], right_points])
        else:
            return np.array([start, end])
    
    def point_to_line_distance(self, point, line_start, line_end):
        """计算点到直线的距离"""
        if np.array_equal(line_start, line_end):
            return np.linalg.norm(point - line_start)
        
        line_vec = line_end - line_start
        point_vec = point - line_start
        line_len = np.linalg.norm(line_vec)
        
        if line_len == 0:
            return np.linalg.norm(point_vec)
        
        line_unitvec = line_vec / line_len
        proj_length = np.dot(point_vec, line_unitvec)
        
        if proj_length < 0:
            return np.linalg.norm(point - line_start)
        elif proj_length > line_len:
            return np.linalg.norm(point - line_end)
        else:
            proj_point = line_start + proj_length * line_unitvec
            return np.linalg.norm(point - proj_point)
    
    def extract_key_points_by_curvature(self, points, num_key_points):
        """基于曲率提取关键点"""
        if len(points) <= num_key_points:
            return points
        
        # 计算曲率
        curvatures = []
        for i in range(1, len(points) - 1):
            p1, p2, p3 = points[i-1], points[i], points[i+1]
            v1 = p2 - p1
            v2 = p3 - p2
            
            if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
                cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                cos_angle = np.clip(cos_angle, -1, 1)
                curvature = 1 - cos_angle
            else:
                curvature = 0
            
            curvatures.append((i, curvature))
        
        # 选择曲率最大的点
        curvatures.sort(key=lambda x: x[1], reverse=True)
        key_indices = [0]  # 起点
        for i, _ in curvatures[:num_key_points-2]:
            key_indices.append(i)
        key_indices.append(len(points)-1)  # 终点
        
        key_indices.sort()
        return points[key_indices]
    
    def calculate_all_algorithms(self):
        """计算所有算法的结果"""
        # 1. 200点插值
        self.interpolated_200 = self.interpolate_trajectory(self.original_points, 200)
        
        # 2. Douglas-Peucker简化
        self.douglas_peucker = self.douglas_peucker_simplify(self.interpolated_200, epsilon=3.0)
        
        # 3. 30个关键点
        self.key_points_30 = self.extract_key_points_by_curvature(self.interpolated_200, 30)
        
        # 4. 50个关键点
        self.key_points_50 = self.extract_key_points_by_curvature(self.interpolated_200, 50)
    
    def draw_trajectory(self, points, color, thickness=2, show_points=False, point_size=3):
        """绘制轨迹"""
        if points is None or len(points) < 2:
            return
        
        # 绘制连线
        for i in range(1, len(points)):
            pt1 = (int(points[i-1, 0]), int(points[i-1, 1]))
            pt2 = (int(points[i, 0]), int(points[i, 1]))
            cv2.line(self.canvas, pt1, pt2, color, thickness)
        
        # 绘制点
        if show_points:
            for point in points:
                cv2.circle(self.canvas, (int(point[0]), int(point[1])), point_size, color, -1)
    
    def draw_comparison(self):
        """绘制对比图"""
        # 清空画布
        self.canvas.fill(0)
        
        # 绘制标题
        cv2.putText(self.canvas, "Path Planning Algorithm Comparison", 
                   (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
        
        # 绘制原始轨迹（灰色）
        self.draw_trajectory(self.original_points, (128, 128, 128), 1)
        
        # 绘制200点插值（黄色细线）
        self.draw_trajectory(self.interpolated_200, (0, 255, 255), 1)
        
        # 绘制Douglas-Peucker结果（蓝色）
        self.draw_trajectory(self.douglas_peucker, (255, 0, 0), 2, show_points=True, point_size=4)
        
        # 绘制30个关键点（绿色）
        self.draw_trajectory(self.key_points_30, (0, 255, 0), 3, show_points=True, point_size=5)
        
        # 绘制50个关键点（红色）
        self.draw_trajectory(self.key_points_50, (0, 0, 255), 2, show_points=True, point_size=4)
        
        # 添加图例
        legend_y = 80
        cv2.putText(self.canvas, f"Original Points: {len(self.original_points)} (Gray)", 
                   (20, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (128, 128, 128), 1)
        
        legend_y += 30
        cv2.putText(self.canvas, f"200-Point Interpolation: {len(self.interpolated_200)} (Yellow)", 
                   (20, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 1)
        
        legend_y += 30
        cv2.putText(self.canvas, f"Douglas-Peucker: {len(self.douglas_peucker)} (Blue)", 
                   (20, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 1)
        
        legend_y += 30
        cv2.putText(self.canvas, f"30 Key Points: {len(self.key_points_30)} (Green) <- RECOMMENDED", 
                   (20, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        legend_y += 30
        cv2.putText(self.canvas, f"50 Key Points: {len(self.key_points_50)} (Red)", 
                   (20, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 1)
        
        # 性能分析
        analysis_y = 300
        cv2.putText(self.canvas, "Performance Analysis for 30ms Laser Control:", 
                   (20, analysis_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
        
        analysis_y += 40
        cv2.putText(self.canvas, "• 200 Points: High precision, large memory (6KB)", 
                   (30, analysis_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        analysis_y += 25
        cv2.putText(self.canvas, "• Douglas-Peucker: Variable points, shape-preserving", 
                   (30, analysis_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        analysis_y += 25
        cv2.putText(self.canvas, "• 30 Key Points: Optimal for 30ms control (0.9KB)", 
                   (30, analysis_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        analysis_y += 25
        cv2.putText(self.canvas, "• 50 Key Points: Higher precision (1.5KB)", 
                   (30, analysis_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        # 推荐方案
        recommendation_y = 500
        cv2.putText(self.canvas, "RECOMMENDATION FOR YOUR APPLICATION:", 
                   (20, recommendation_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        recommendation_y += 40
        cv2.putText(self.canvas, "Use 30-50 Key Points Algorithm:", 
                   (30, recommendation_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        recommendation_y += 30
        cv2.putText(self.canvas, "✓ Fast 30ms cycle time", 
                   (50, recommendation_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        
        recommendation_y += 25
        cv2.putText(self.canvas, "✓ Low memory usage", 
                   (50, recommendation_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        
        recommendation_y += 25
        cv2.putText(self.canvas, "✓ Preserves important shape features", 
                   (50, recommendation_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        
        recommendation_y += 25
        cv2.putText(self.canvas, "✓ Smooth laser movement", 
                   (50, recommendation_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        
        # 显示时间信息
        cv2.putText(self.canvas, f"Generated at: {time.strftime('%H:%M:%S')}", 
                   (self.window_width - 200, self.window_height - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 128), 1)
    
    def run(self):
        """运行对比展示"""
        print("=== 路径规划算法对比 ===")
        print(f"原始点数: {len(self.original_points)}")
        print(f"200点插值: {len(self.interpolated_200)}")
        print(f"Douglas-Peucker: {len(self.douglas_peucker)}")
        print(f"30个关键点: {len(self.key_points_30)}")
        print(f"50个关键点: {len(self.key_points_50)}")
        print("\n推荐使用: 30-50个关键点算法")
        print("按任意键退出...")
        
        while True:
            self.draw_comparison()
            cv2.imshow('Path Planning Comparison', self.canvas)
            
            key = cv2.waitKey(1000) & 0xFF  # 每秒更新一次
            if key != 255:  # 按任意键退出
                break
        
        cv2.destroyAllWindows()

if __name__ == "__main__":
    comparison = PathPlanningComparison()
    comparison.run()
