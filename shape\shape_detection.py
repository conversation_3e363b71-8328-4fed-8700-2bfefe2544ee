import cv2
import numpy as np
import time
import math

# 矩形检测参数
MIN_RECT_AREA = 1000      # 最小矩形面积
MAX_RECT_AREA = 100000    # 最大矩形面积
MIN_ASPECT_RATIO = 0.3    # 最小宽高比
MAX_ASPECT_RATIO = 3.0    # 最大宽高比
MIN_COMPACTNESS = 0.7     # 最小紧密度（面积填充率）

# 透视变换参数
PERSPECTIVE_ENABLED = True   # 是否启用透视变换
PERSPECTIVE_SIZE = 400       # 透视变换后的标准尺寸

# 内框自动检测参数
AUTO_DETECT_INNER_FRAME = True  # 是否自动检测内框并设为ROI
FRAME_THICKNESS_RATIO = 0.1     # 框线厚度比例（相对于矩形尺寸）
MIN_INNER_RATIO = 0.6           # 内框最小尺寸比例（相对于外框）

# 调试显示相关变量
show_debug_windows = True  # 是否显示调试窗口
show_edges = True         # 是否显示边缘检测结果
show_contours = True      # 是否显示轮廓检测结果
show_detection_boxes = False  # 是否显示检测框（设为False隐藏框）

# ROI相关变量
roi_selected = False
roi_start_point = None
roi_end_point = None
roi_rect = None  # (x, y, w, h)
selecting_roi = False

def order_points(pts):
    """对四个点进行排序：左上、右上、右下、左下"""
    # 初始化坐标列表
    rect = np.zeros((4, 2), dtype="float32")

    # 左上角的点具有最小的和，右下角的点具有最大的和
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]  # 左上
    rect[2] = pts[np.argmax(s)]  # 右下

    # 右上角的点具有最小的差值，左下角的点具有最大的差值
    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmin(diff)]  # 右上
    rect[3] = pts[np.argmax(diff)]  # 左下

    return rect

def four_point_transform(image, pts):
    """执行四点透视变换"""
    # 获取有序的坐标点
    rect = order_points(pts)
    (tl, tr, br, bl) = rect

    # 计算新图像的宽度
    widthA = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
    widthB = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
    maxWidth = max(int(widthA), int(widthB))

    # 计算新图像的高度
    heightA = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
    heightB = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
    maxHeight = max(int(heightA), int(heightB))

    # 构建目标点集（矩形化后的坐标）
    dst = np.array([
        [0, 0],
        [maxWidth - 1, 0],
        [maxWidth - 1, maxHeight - 1],
        [0, maxHeight - 1]], dtype="float32")

    # 计算透视变换矩阵并应用
    M = cv2.getPerspectiveTransform(rect, dst)
    warped = cv2.warpPerspective(image, M, (maxWidth, maxHeight))

    return warped, M

def detect_roi_rectangle(img):
    """检测ROI区域内的主要矩形，用于透视变换"""
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 50, 150)

    # 形态学操作
    kernel = np.ones((3, 3), np.uint8)
    edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 寻找最大的四边形轮廓
    for contour in sorted(contours, key=cv2.contourArea, reverse=True):
        area = cv2.contourArea(contour)
        if area < 1000:  # 面积太小，跳过
            continue

        perimeter = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.02 * perimeter, True)

        # 如果找到四边形
        if len(approx) == 4:
            return approx.reshape(4, 2)

    return None

def detect_inner_frame_and_set_roi(img):
    """检测图像中的内框矩形，并自动设为ROI"""
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 50, 150)

    # 形态学操作
    kernel = np.ones((3, 3), np.uint8)
    edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 寻找合适的矩形作为内框
    for contour in sorted(contours, key=cv2.contourArea, reverse=True):
        area = cv2.contourArea(contour)

        # 面积筛选
        if area < MIN_RECT_AREA or area > MAX_RECT_AREA:
            continue

        perimeter = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.02 * perimeter, True)

        # 检查是否为四边形
        if len(approx) == 4:
            # 计算边界矩形
            x, y, w, h = cv2.boundingRect(approx)
            aspect_ratio = float(w) / h

            # 宽高比检查
            if MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO:
                # 紧密度检查
                rect_area = w * h
                compactness = area / rect_area if rect_area > 0 else 0

                if compactness >= MIN_COMPACTNESS:
                    print(f"✓ 检测到内框矩形: 位置({x}, {y}), 尺寸({w}x{h})")
                    print(f"  面积: {area:.0f}, 宽高比: {aspect_ratio:.2f}, 紧密度: {compactness:.2f}")
                    return (x, y, w, h)

    print("⚠ 未检测到合适的内框矩形")
    return None



# 鼠标回调函数
def mouse_callback(event, x, y, flags, param):
    global roi_start_point, roi_end_point, roi_selected, selecting_roi, roi_rect

    if event == cv2.EVENT_LBUTTONDOWN:
        # 开始选择ROI
        roi_start_point = (x, y)
        roi_end_point = (x, y)
        selecting_roi = True
        roi_selected = False

    elif event == cv2.EVENT_MOUSEMOVE and selecting_roi:
        # 拖拽过程中更新终点
        roi_end_point = (x, y)

    elif event == cv2.EVENT_LBUTTONUP:
        # 完成ROI选择
        if selecting_roi:
            roi_end_point = (x, y)
            selecting_roi = False

            # 计算ROI矩形
            x1, y1 = roi_start_point
            x2, y2 = roi_end_point

            # 确保坐标正确（左上角和右下角）
            roi_x = min(x1, x2)
            roi_y = min(y1, y2)
            roi_w = abs(x2 - x1)
            roi_h = abs(y2 - y1)

            # 只有当ROI有一定大小时才认为有效
            if roi_w > 20 and roi_h > 20:
                roi_rect = (roi_x, roi_y, roi_w, roi_h)
                roi_selected = True
                print(f"ROI区域已选择: ({roi_x}, {roi_y}, {roi_w}, {roi_h})")
            else:
                roi_selected = False
                roi_rect = None
                print("ROI区域太小，已取消选择")

# 初始化摄像头 (使用默认摄像头，通常是0)
cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

print("专业矩形检测程序已启动...")
print(f"矩形面积范围: {MIN_RECT_AREA} - {MAX_RECT_AREA}")
print(f"宽高比范围: {MIN_ASPECT_RATIO} - {MAX_ASPECT_RATIO}")
print(f"最小紧密度: {MIN_COMPACTNESS}")
print("操作说明:")
print("- 鼠标左键拖拽选择ROI区域")
print("- 按 'r' 键重置ROI区域（全帧检测）")
print("- 按 'p' 键切换透视变换功能")
print("- 按 'i' 键切换自动内框检测功能")
print("- 按 'b' 键切换检测框显示")
print("- 按 'e' 键切换边缘检测窗口显示")
print("- 按 'c' 键切换轮廓检测窗口显示")
print("- 按 'd' 键切换所有调试窗口显示")
print("- 按 'q' 键退出程序")
print("检测标准:")
print("  ✓Rectangle: 通过所有检测条件的高质量矩形")
print("  ✓Corrected Rectangle: 透视变换校正后的矩形")
print("  ✗Loose Quad: 四边形但紧密度不足")
print("  ✗Narrow Quad: 四边形但宽高比不合适")
print("ROI模式:")
print("  手动模式: 鼠标拖拽选择ROI区域")
print("  自动模式: 自动检测内框矩形并设为ROI")
print("  黄色框: 当前活动的ROI区域")

# 设置鼠标回调
cv2.namedWindow('Shape Detection')
cv2.setMouseCallback('Shape Detection', mouse_callback)

# 创建调试窗口
if show_debug_windows:
    if show_edges:
        cv2.namedWindow('Edges', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Edges', 400, 300)
    if show_contours:
        cv2.namedWindow('Contours', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Contours', 400, 300)

frame_count = 0

while True:
    frame_count += 1

    # 读取图像
    ret, img = cap.read()
    if not ret:
        print("无法读取摄像头画面")
        break

    # 创建显示用的图像副本
    img_display = img.copy()

    # 获取图像尺寸
    height, width = img.shape[:2]
    center_x, center_y = width // 2, height // 2

    # 绘制图像中心点
    cv2.line(img_display, (center_x - 10, center_y), (center_x + 10, center_y), (255, 0, 0), 2)
    cv2.line(img_display, (center_x, center_y - 10), (center_x, center_y + 10), (255, 0, 0), 2)
    cv2.circle(img_display, (center_x, center_y), 3, (255, 0, 0), -1)

    # 自动检测内框并设为ROI（如果启用且未手动选择ROI）
    if AUTO_DETECT_INNER_FRAME and not roi_selected:
        detected_inner_frame = detect_inner_frame_and_set_roi(img)
        if detected_inner_frame:
            roi_rect = detected_inner_frame
            roi_selected = True
            print("✓ 自动检测到内框，已设为ROI区域")

    # 确定处理区域
    if roi_selected and roi_rect is not None:
        # 使用ROI区域
        roi_x, roi_y, roi_w, roi_h = roi_rect
        # 确保ROI在图像范围内
        roi_x = max(0, min(roi_x, width - 1))
        roi_y = max(0, min(roi_y, height - 1))
        roi_w = min(roi_w, width - roi_x)
        roi_h = min(roi_h, height - roi_y)

        # 提取ROI区域
        img_roi = img[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]

        # 绘制ROI边框
        cv2.rectangle(img_display, (roi_x, roi_y), (roi_x + roi_w, roi_y + roi_h), (0, 255, 255), 2)
        cv2.putText(img_display, "ROI", (roi_x, roi_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

        # 尝试透视变换矫正ROI区域
        if PERSPECTIVE_ENABLED:
            roi_corners = detect_roi_rectangle(img_roi)
            if roi_corners is not None:
                # 执行透视变换
                warped_img, transform_matrix = four_point_transform(img_roi, roi_corners)
                img_roi = warped_img
                roi_x, roi_y = 0, 0  # 透视变换后重置偏移
                print("✓ 透视变换已应用")

                # 显示透视变换后的图像
                if show_debug_windows:
                    cv2.imshow('Perspective Corrected', warped_img)
            else:
                print("⚠ 未检测到透视变换目标，使用原始ROI")

        # 转换为灰度图
        gray = cv2.cvtColor(img_roi, cv2.COLOR_BGR2GRAY)
    else:
        # 使用全帧（警告：可能包含框线干扰）
        img_roi = img
        roi_x, roi_y = 0, 0
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        print("⚠ 未选择ROI，在全图检测（可能包含框线干扰）")

    # 高斯模糊去噪
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # 边缘检测
    edges = cv2.Canny(blurred, 50, 150)

    # 形态学操作 - 连接断开的边缘，增强矩形检测
    kernel = np.ones((3, 3), np.uint8)
    edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    rectangles = []

    print(f"检测到 {len(contours)} 个轮廓")

    # 创建轮廓可视化图像
    if show_debug_windows and show_contours:
        # 创建彩色轮廓图像
        contour_img = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
        # 绘制所有轮廓（不同颜色）
        for i, contour in enumerate(contours):
            # 使用不同颜色绘制轮廓
            color = ((i * 50) % 255, (i * 80) % 255, (i * 120) % 255)
            cv2.drawContours(contour_img, [contour], -1, color, 2)

            # 显示轮廓面积
            area = cv2.contourArea(contour)
            if area > 100:  # 只显示较大轮廓的面积
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    cv2.putText(contour_img, f"{int(area)}", (cx-20, cy),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    # 统计检测到的形状
    shape_count = {}

    # 遍历所有轮廓
    for i, contour in enumerate(contours):
        # 调整轮廓坐标到全图坐标系（如果使用了ROI）
        if roi_selected and roi_rect is not None:
            # 将ROI内的轮廓坐标转换为全图坐标
            contour_adjusted = contour.copy()
            contour_adjusted[:, :, 0] += roi_x  # x坐标偏移
            contour_adjusted[:, :, 1] += roi_y  # y坐标偏移
        else:
            contour_adjusted = contour

        # 检测形状
        # 计算轮廓周长
        perimeter = cv2.arcLength(contour, True)

        # 多边形逼近
        approx = cv2.approxPolyDP(contour, 0.02 * perimeter, True)

        # 计算轮廓面积
        area = cv2.contourArea(contour)

        # 专业矩形检测算法
        shape_name = "Invalid"

        # 第1步：面积筛选
        if area < MIN_RECT_AREA or area > MAX_RECT_AREA:
            shape_name = "Small/Large"
        else:
            # 第2步：四边形检查
            vertices = len(approx)
            if vertices == 4:
                # 第3步：计算边界矩形和宽高比
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = float(w) / h if h > 0 else 0

                print(f"  矩形候选: 位置({x},{y}), 尺寸({w}x{h}), 宽高比={aspect_ratio:.2f}")

                # 第4步：宽高比筛选
                if MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO:
                    # 第5步：紧密度检查（面积填充率）
                    rect_area = w * h
                    compactness = area / rect_area if rect_area > 0 else 0

                    print(f"    紧密度: {compactness:.3f}")

                    if compactness >= MIN_COMPACTNESS:
                        shape_name = f"✓Rectangle (AR:{aspect_ratio:.2f}, C:{compactness:.2f})"
                        print(f"    ✅ 矩形已确认: 面积={area:.0f}, 紧密度={compactness:.3f}")
                    else:
                        shape_name = f"✗Loose Quad (C:{compactness:.2f})"
                        print(f"    ❌ 紧密度不足 (需要>{MIN_COMPACTNESS})")
                else:
                    shape_name = f"✗Narrow Quad (AR:{aspect_ratio:.2f})"
                    print(f"    ❌ 宽高比不符合 (需要{MIN_ASPECT_RATIO}-{MAX_ASPECT_RATIO})")
            elif vertices == 3:
                shape_name = "Triangle"
                print(f"  ❌ 不是四边形 (顶点数: {vertices}) - 三角形")
            elif vertices > 4:
                # 检查是否接近圆形
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter * perimeter)
                    if circularity > 0.7:
                        shape_name = "Circle"
                        print(f"  ❌ 不是四边形 (顶点数: {vertices}) - 圆形")
                    else:
                        shape_name = f"Polygon ({vertices})"
                        print(f"  ❌ 不是四边形 (顶点数: {vertices}) - 多边形")
                else:
                    shape_name = "Unknown"
                    print(f"  ❌ 不是四边形 (顶点数: {vertices}) - 未知形状")
            else:
                shape_name = "Line/Point"
                print(f"  ❌ 不是四边形 (顶点数: {vertices}) - 线条/点")

        # 跳过小图形
        if shape_name == "Small":
            continue

        # 统计形状数量
        if shape_name in shape_count:
            shape_count[shape_name] += 1
        else:
            shape_count[shape_name] = 1

        # 调整多边形逼近坐标到全图坐标系
        if roi_selected and roi_rect is not None:
            approx_adjusted = approx.copy()
            approx_adjusted[:, :, 0] += roi_x
            approx_adjusted[:, :, 1] += roi_y
        else:
            approx_adjusted = approx

        # 绘制轮廓（使用调整后的坐标）- 可控制显示
        if show_detection_boxes:
            cv2.drawContours(img_display, [contour_adjusted], -1, (0, 255, 0), 2)

        # 绘制多边形逼近结果（使用调整后的坐标）- 可控制显示
        if show_detection_boxes:
            cv2.drawContours(img_display, [approx_adjusted], -1, (255, 0, 255), 2)

        # 计算轮廓中心点
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])

            # 如果是透视变换后的坐标，添加标记
            if PERSPECTIVE_ENABLED and roi_selected and 'roi_corners' in locals() and roi_corners is not None:
                if "✓Rectangle" in shape_name:
                    shape_name = shape_name.replace("✓Rectangle", "✓Corrected Rectangle")

            # 调整中心点坐标到全图坐标系
            if roi_selected and roi_rect is not None:
                cx_display = cx + roi_x
                cy_display = cy + roi_y
            else:
                cx_display = cx
                cy_display = cy

            # 在形状中心绘制文本 - 可控制显示
            if show_detection_boxes:
                cv2.putText(img_display, shape_name, (cx_display - 20, cy_display),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

            # 绘制形状中心点 - 可控制显示
            if show_detection_boxes:
                cv2.circle(img_display, (cx_display, cy_display), 3, (0, 0, 255), -1)

            # 串口打印检测结果（使用全图坐标）
            print(f"{shape_name} ({cx_display},{cy_display}) 轮廓面积: {int(area)}")
    
    # 绘制正在选择的ROI
    if selecting_roi and roi_start_point is not None and roi_end_point is not None:
        x1, y1 = roi_start_point
        x2, y2 = roi_end_point
        cv2.rectangle(img_display, (x1, y1), (x2, y2), (0, 255, 255), 2)
        cv2.putText(img_display, "Selecting ROI...", (x1, y1 - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

    # 在图像顶部显示统计信息
    y_offset = 20
    cv2.putText(img_display, f"Frame: {frame_count}",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    y_offset += 15
    cv2.putText(img_display, f"Image Center: ({center_x}, {center_y})",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    y_offset += 15
    # 显示ROI状态
    if roi_selected and roi_rect is not None:
        roi_x, roi_y, roi_w, roi_h = roi_rect
        cv2.putText(img_display, f"ROI: ({roi_x}, {roi_y}, {roi_w}, {roi_h})",
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    else:
        cv2.putText(img_display, "ROI: Full Frame",
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    y_offset += 15
    cv2.putText(img_display, f"Area: {MIN_RECT_AREA}-{MAX_RECT_AREA}, AR: {MIN_ASPECT_RATIO}-{MAX_ASPECT_RATIO}, C: >{MIN_COMPACTNESS}",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 255, 255), 1)

    y_offset += 15
    perspective_status = "ON" if PERSPECTIVE_ENABLED else "OFF"
    cv2.putText(img_display, f"Perspective Correction: {perspective_status}",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255) if PERSPECTIVE_ENABLED else (128, 128, 128), 1)

    y_offset += 15
    auto_inner_status = "ON" if AUTO_DETECT_INNER_FRAME else "OFF"
    cv2.putText(img_display, f"Auto Inner Frame: {auto_inner_status}",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 165, 0) if AUTO_DETECT_INNER_FRAME else (128, 128, 128), 1)

    y_offset += 15
    boxes_status = "ON" if show_detection_boxes else "OFF"
    cv2.putText(img_display, f"Detection Boxes: {boxes_status}",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0) if show_detection_boxes else (128, 128, 128), 1)

    y_offset += 15
    cv2.putText(img_display, "Controls: Mouse=ROI, R=Reset, E=Edges, C=Contours, D=Debug, Q=Quit",
               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 255, 0), 1)

    y_offset += 20
    for shape, count in shape_count.items():
        cv2.putText(img_display, f"{shape}: {count}",
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        y_offset += 15

    # 显示主窗口
    cv2.imshow('Shape Detection', img_display)

    # 显示调试窗口
    if show_debug_windows:
        if show_edges:
            cv2.imshow('Edges', edges)
        if show_contours and 'contour_img' in locals():
            cv2.imshow('Contours', contour_img)

    # 检查按键
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord('r') or key == ord('R'):
        # 重置ROI区域
        roi_selected = False
        roi_rect = None
        roi_start_point = None
        roi_end_point = None
        selecting_roi = False
        print("ROI区域已重置，恢复全帧检测")
    elif key == ord('p') or key == ord('P'):
        # 切换透视变换功能
        PERSPECTIVE_ENABLED = not PERSPECTIVE_ENABLED
        print(f"透视变换功能: {'开启' if PERSPECTIVE_ENABLED else '关闭'}")
        if PERSPECTIVE_ENABLED:
            print("  - 将自动检测ROI内的主要矩形并进行透视校正")
            print("  - 校正后的矩形将标记为 '✓Corrected Rectangle'")
        else:
            print("  - 将使用原始ROI区域进行检测")
    elif key == ord('i') or key == ord('I'):
        # 切换内外框检测功能
        DETECT_INNER_FRAME = not DETECT_INNER_FRAME
        AUTO_DETECT_INNER_FRAME = not AUTO_DETECT_INNER_FRAME
        print(f"自动内框检测功能: {'开启' if AUTO_DETECT_INNER_FRAME else '关闭'}")
        if AUTO_DETECT_INNER_FRAME:
            print("  - 自动检测图像中的内框矩形")
            print("  - 将检测到的内框自动设为ROI区域")
            print("  - 在内框内进行矩形检测")
        else:
            print("  - 需要手动选择ROI区域")
    elif key == ord('b') or key == ord('B'):
        # 切换检测框显示
        show_detection_boxes = not show_detection_boxes
        print(f"检测框显示: {'开启' if show_detection_boxes else '关闭'}")
        if show_detection_boxes:
            print("  - 将显示轮廓框、文本标签和中心点")
        else:
            print("  - 隐藏所有检测框，只保留检测功能")
    elif key == ord('e') or key == ord('E'):
        # 切换边缘检测窗口
        show_edges = not show_edges
        if show_edges:
            cv2.namedWindow('Edges', cv2.WINDOW_NORMAL)
            cv2.resizeWindow('Edges', 400, 300)
            print("边缘检测窗口已开启")
        else:
            cv2.destroyWindow('Edges')
            print("边缘检测窗口已关闭")
    elif key == ord('c') or key == ord('C'):
        # 切换轮廓检测窗口
        show_contours = not show_contours
        if show_contours:
            cv2.namedWindow('Contours', cv2.WINDOW_NORMAL)
            cv2.resizeWindow('Contours', 400, 300)
            print("轮廓检测窗口已开启")
        else:
            cv2.destroyWindow('Contours')
            print("轮廓检测窗口已关闭")
    elif key == ord('d') or key == ord('D'):
        # 切换所有调试窗口
        show_debug_windows = not show_debug_windows
        if show_debug_windows:
            if show_edges:
                cv2.namedWindow('Edges', cv2.WINDOW_NORMAL)
                cv2.resizeWindow('Edges', 400, 300)
            if show_contours:
                cv2.namedWindow('Contours', cv2.WINDOW_NORMAL)
                cv2.resizeWindow('Contours', 400, 300)
            print("调试窗口已开启")
        else:
            cv2.destroyWindow('Edges')
            cv2.destroyWindow('Contours')
            print("调试窗口已关闭")

# 释放资源
cap.release()
cv2.destroyAllWindows()
