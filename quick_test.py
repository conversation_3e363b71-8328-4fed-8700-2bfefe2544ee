#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速矩形检测测试
直接启动摄像头进行实时矩形检测
"""

import cv2
from rectangle_detector import RectangleDetector

def main():
    print("启动矩形检测...")
    print("按 'q' 退出")
    
    # 创建检测器
    detector = RectangleDetector()
    
    # 打开摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("无法打开摄像头")
        return
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # 进行矩形检测
        result_img, rectangles = detector.detect(frame)
        
        # 显示检测信息
        info_text = f"检测到 {len(rectangles)} 个矩形"
        cv2.putText(result_img, info_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # 显示最大矩形信息
        if rectangles:
            largest = rectangles[0]
            detail_text = f"最大矩形: 面积={largest['area']:.0f}, 长宽比={largest['aspect_ratio']:.2f}"
            cv2.putText(result_img, detail_text, (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        
        cv2.imshow('矩形检测', result_img)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
