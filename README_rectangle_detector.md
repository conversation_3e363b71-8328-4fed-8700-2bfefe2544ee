# 矩形检测器 (Rectangle Detector)

一个专门用于检测图像中矩形物体的Python模块，特别适用于A4纸、文档、卡片等矩形物体的识别。

## 功能特点

- **多种检测模式**: 支持A4纸、通用矩形、小矩形、大矩形等多种预设配置
- **高精度检测**: 基于HSV颜色空间、轮廓分析、多边形逼近等多重筛选
- **灵活配置**: 支持自定义检测参数，适应不同应用场景
- **实时检测**: 支持摄像头实时检测和静态图像检测
- **中文友好**: 完整的中文注释和文档

## 文件结构

```
rectangle_detector/
├── rectangle_detector.py          # 核心检测器类
├── rectangle_config.py            # 配置管理器和预设参数
├── rectangle_detector_example.py  # 使用示例和测试代码
└── README_rectangle_detector.md   # 说明文档
```

## 快速开始

### 1. 基本使用

```python
from rectangle_detector import RectangleDetector
import cv2

# 创建检测器
detector = RectangleDetector()

# 读取图像
img = cv2.imread('your_image.jpg')

# 进行检测
result_img, rectangles = detector.detect(img)

# 显示结果
cv2.imshow('检测结果', result_img)
cv2.waitKey(0)
```

### 2. 使用预设配置

```python
from rectangle_detector import RectangleDetector
from rectangle_config import RectangleConfigManager

# 获取A4纸检测配置
config = RectangleConfigManager.get_config('a4_paper')
detector = RectangleDetector(config)

# 或者直接使用其他预设
small_config = RectangleConfigManager.get_config('small')
detector = RectangleDetector(small_config)
```

### 3. 自定义配置

```python
# 创建自定义配置
custom_config = {
    'min_area': 1000,
    'max_area': 50000,
    'target_aspect_ratio': 1.5,
    'contour_color': (255, 0, 0),  # 蓝色轮廓
}

detector = RectangleDetector(custom_config)
```

## 预设配置说明

| 预设名称 | 适用场景 | 特点 |
|---------|---------|------|
| `a4_paper` | A4纸检测 | 标准1.414长宽比，中等面积范围 |
| `general` | 通用矩形 | 宽松的检测条件，适用于各种矩形 |
| `small` | 小矩形物体 | 小面积范围，宽松长宽比 |
| `large` | 大矩形物体 | 大面积范围，强形态学处理 |
| `high_precision` | 高精度检测 | 严格的检测条件，高精度逼近 |

## 检测参数详解

### HSV颜色检测
- `lower_white_hsv`: HSV下限 [H, S, V]
- `upper_white_hsv`: HSV上限 [H, S, V]

### 面积筛选
- `min_area`: 最小面积阈值
- `max_area`: 最大面积阈值

### 长宽比验证
- `target_aspect_ratio`: 目标长宽比
- `aspect_tolerance`: 长宽比容差

### 形态学操作
- `do_morphology`: 是否启用形态学操作
- `kernel_size`: 形态学核大小
- `morph_iterations`: 迭代次数

### 多边形逼近
- `approx_epsilon_ratio`: 逼近精度比例

### 显示设置
- `draw_contour`: 是否绘制轮廓
- `draw_center`: 是否绘制中心点
- `contour_color`: 轮廓颜色 (B, G, R)
- `center_color`: 中心点颜色 (B, G, R)
- `text_color`: 文字颜色 (B, G, R)

## 检测结果格式

每个检测到的矩形包含以下信息：

```python
rectangle = {
    'contour': contour,           # OpenCV轮廓对象
    'approx': approx,            # 多边形逼近结果
    'corners': corners,          # 四个角点坐标 [左上, 右上, 右下, 左下]
    'center': (x, y),           # 中心点坐标
    'area': area,               # 面积
    'aspect_ratio': ratio,      # 长宽比
    'rect': rect                # 最小外接矩形
}
```

## 使用示例

### 摄像头实时检测

```python
import cv2
from rectangle_detector import RectangleDetector

detector = RectangleDetector()
cap = cv2.VideoCapture(0)

while True:
    ret, frame = cap.read()
    if not ret:
        break
    
    result_img, rectangles = detector.detect(frame)
    
    # 显示检测数量
    info = f"检测到 {len(rectangles)} 个矩形"
    cv2.putText(result_img, info, (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    
    cv2.imshow('矩形检测', result_img)
    
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
```

### 获取最大矩形

```python
# 只获取最大的矩形
largest_rect = detector.get_largest_rectangle(img)

if largest_rect:
    print(f"最大矩形面积: {largest_rect['area']}")
    print(f"中心点: {largest_rect['center']}")
    print(f"四个角点: {largest_rect['corners']}")
```

### 动态更新配置

```python
# 运行时更新配置
new_config = {
    'min_area': 2000,
    'contour_color': (0, 0, 255),  # 红色轮廓
}
detector.update_config(new_config)

# 查看当前配置
current_config = detector.get_config()
print(current_config)
```

## 运行测试

```bash
# 运行完整测试示例
python rectangle_detector_example.py

# 查看配置管理器
python rectangle_config.py
```

## 依赖要求

- Python 3.6+
- OpenCV (cv2)
- NumPy

## 安装依赖

```bash
pip install opencv-python numpy
```

## 注意事项

1. **光照条件**: 确保检测环境有良好的光照，避免阴影干扰
2. **背景对比**: 矩形物体与背景要有足够的颜色对比度
3. **参数调优**: 根据实际应用场景调整HSV范围和面积阈值
4. **性能优化**: 大图像建议先缩放再检测，提高处理速度

## 常见问题

**Q: 检测不到矩形怎么办？**
A: 检查HSV颜色范围是否合适，尝试调整面积阈值和长宽比容差。

**Q: 检测到太多误检怎么办？**
A: 使用更严格的配置如`high_precision`，或者缩小面积范围。

**Q: 如何检测彩色矩形？**
A: 修改HSV颜色范围参数，针对特定颜色进行检测。

## 更新日志

- v1.0.0: 初始版本，支持基本矩形检测功能
- 支持多种预设配置
- 完整的中文文档和注释
