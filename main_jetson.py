"""
Threshold tuning version of Jetson A4 paper and purple laser detector.

This script adds a control window with trackbars to adjust HSV and shape
thresholds for both the purple laser detection and the A4 paper detection
in real time. You can press 's' while the program runs to save the current
threshold values to a JSON file, and press 'r' to reload them. Press 'q'
to exit. If the ``serial_comm.SerialComm`` module is available, it will be
used for UART communication; otherwise a stub will print the coordinates.
"""

import cv2
import numpy as np
import time
import json
import os


# Try to import the real SerialComm class, otherwise define a stub
try:
    from serial_comm import SerialComm  # type: ignore
except ImportError:
    class SerialComm:
        def __init__(self, port, baudrate, enable=True):
            print("[SerialComm] Using stub; no real UART communication will occur.")
        def init(self):
            return True
        def send_red_laser_point(self, point):
            x, y = point
            print(f"[Serial] send_red_laser_point: {x},{y}")
        def close(self):
            pass


# --------------------------- Default thresholds ---------------------------
DEFAULT_THRESHOLDS = {
    # Purple laser HSV thresholds
    "laser_h_min": 54,
    "laser_h_max": 230,
    "laser_s_min": 30,
    "laser_s_max": 178,
    "laser_v_min": 56,
    "laser_v_max": 255,
    # Laser shape constraints
    "laser_min_area": 5,
    "laser_max_area": 200,
    "laser_min_circularity": 30,  # multiplied by 100 for trackbar (0‑100)
    "laser_min_brightness": 150,
    # White HSV thresholds for A4 detection
    "white_h_min": 0,
    "white_h_max": 250,
    "white_s_min": 0,
    "white_s_max": 20,
    "white_v_min": 0,
    "white_v_max": 250,
    # A4 area limits (scaled by 100 for trackbars)
    "a4_min_area": 50,
    "a4_max_area": 1000,
}

THRESHOLD_FILE = "thresholds.json"


def load_thresholds() -> dict:
    """Load thresholds from a JSON file or return defaults."""
    if os.path.exists(THRESHOLD_FILE):
        try:
            with open(THRESHOLD_FILE, "r") as f:
                data = json.load(f)
                return {**DEFAULT_THRESHOLDS, **data}
        except Exception as e:
            print(f"Failed to load thresholds: {e}")
    return DEFAULT_THRESHOLDS.copy()


def save_thresholds(thresholds: dict) -> None:
    """Save thresholds to a JSON file."""
    try:
        with open(THRESHOLD_FILE, "w") as f:
            json.dump(thresholds, f, indent=4)
        print(f"Thresholds saved to {THRESHOLD_FILE}")
    except Exception as e:
        print(f"Failed to save thresholds: {e}")


def create_trackbars(thresholds: dict) -> None:
    """Create OpenCV trackbars for adjusting detection thresholds."""
    cv2.namedWindow("Controls", cv2.WINDOW_NORMAL)
    # Laser HSV
    cv2.createTrackbar("Laser H min", "Controls", thresholds["laser_h_min"], 255, lambda x: None)
    cv2.createTrackbar("Laser H max", "Controls", thresholds["laser_h_max"], 255, lambda x: None)
    cv2.createTrackbar("Laser S min", "Controls", thresholds["laser_s_min"], 255, lambda x: None)
    cv2.createTrackbar("Laser S max", "Controls", thresholds["laser_s_max"], 255, lambda x: None)
    cv2.createTrackbar("Laser V min", "Controls", thresholds["laser_v_min"], 255, lambda x: None)
    cv2.createTrackbar("Laser V max", "Controls", thresholds["laser_v_max"], 255, lambda x: None)
    # Laser shape
    cv2.createTrackbar("Laser Min Area", "Controls", thresholds["laser_min_area"], 500, lambda x: None)
    cv2.createTrackbar("Laser Max Area", "Controls", thresholds["laser_max_area"], 2000, lambda x: None)
    cv2.createTrackbar("Laser Min Circ*100", "Controls", thresholds["laser_min_circularity"], 100, lambda x: None)
    cv2.createTrackbar("Laser Min Bright", "Controls", thresholds["laser_min_brightness"], 255, lambda x: None)
    # White HSV for A4
    cv2.createTrackbar("White H min", "Controls", thresholds["white_h_min"], 255, lambda x: None)
    cv2.createTrackbar("White H max", "Controls", thresholds["white_h_max"], 255, lambda x: None)
    cv2.createTrackbar("White S min", "Controls", thresholds["white_s_min"], 255, lambda x: None)
    cv2.createTrackbar("White S max", "Controls", thresholds["white_s_max"], 255, lambda x: None)
    cv2.createTrackbar("White V min", "Controls", thresholds["white_v_min"], 255, lambda x: None)
    cv2.createTrackbar("White V max", "Controls", thresholds["white_v_max"], 255, lambda x: None)
    # A4 area scaled (trackbar values represent area x100)
    cv2.createTrackbar("A4 Min Area x100", "Controls", thresholds["a4_min_area"], 2000, lambda x: None)
    cv2.createTrackbar("A4 Max Area x100", "Controls", thresholds["a4_max_area"], 3000, lambda x: None)


def read_trackbars(thresholds: dict) -> None:
    """Update the threshold dictionary from current trackbar positions."""
    thresholds["laser_h_min"] = cv2.getTrackbarPos("Laser H min", "Controls")
    thresholds["laser_h_max"] = cv2.getTrackbarPos("Laser H max", "Controls")
    thresholds["laser_s_min"] = cv2.getTrackbarPos("Laser S min", "Controls")
    thresholds["laser_s_max"] = cv2.getTrackbarPos("Laser S max", "Controls")
    thresholds["laser_v_min"] = cv2.getTrackbarPos("Laser V min", "Controls")
    thresholds["laser_v_max"] = cv2.getTrackbarPos("Laser V max", "Controls")
    thresholds["laser_min_area"] = cv2.getTrackbarPos("Laser Min Area", "Controls")
    thresholds["laser_max_area"] = cv2.getTrackbarPos("Laser Max Area", "Controls")
    thresholds["laser_min_circularity"] = cv2.getTrackbarPos("Laser Min Circ*100", "Controls")
    thresholds["laser_min_brightness"] = cv2.getTrackbarPos("Laser Min Bright", "Controls")
    thresholds["white_h_min"] = cv2.getTrackbarPos("White H min", "Controls")
    thresholds["white_h_max"] = cv2.getTrackbarPos("White H max", "Controls")
    thresholds["white_s_min"] = cv2.getTrackbarPos("White S min", "Controls")
    thresholds["white_s_max"] = cv2.getTrackbarPos("White S max", "Controls")
    thresholds["white_v_min"] = cv2.getTrackbarPos("White V min", "Controls")
    thresholds["white_v_max"] = cv2.getTrackbarPos("White V max", "Controls")
    thresholds["a4_min_area"] = cv2.getTrackbarPos("A4 Min Area x100", "Controls")
    thresholds["a4_max_area"] = cv2.getTrackbarPos("A4 Max Area x100", "Controls")


# --------------------------- Detector classes ---------------------------
class PurpleLaserDetector:
    def __init__(self, thresholds: dict, pixel_radius: int = 3):
        self.thresholds = thresholds
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)

    def detect(self, img: np.ndarray):
        """Detect the brightest purple laser point based on current thresholds."""
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        # Build lower/upper arrays
        lower = np.array([
            self.thresholds["laser_h_min"],
            self.thresholds["laser_s_min"],
            self.thresholds["laser_v_min"],
        ])
        upper = np.array([
            self.thresholds["laser_h_max"],
            self.thresholds["laser_s_max"],
            self.thresholds["laser_v_max"],
        ])
        mask = cv2.inRange(hsv, lower, upper)
        # Morphological operations
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, self.kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, self.kernel)
        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        best_point = None
        best_score = -1
        for cnt in contours:
            area = cv2.contourArea(cnt)
            min_area = self.thresholds["laser_min_area"]
            max_area = self.thresholds["laser_max_area"]
            if not (min_area <= area <= max_area):
                continue
            perimeter = cv2.arcLength(cnt, True)
            if perimeter == 0:
                continue
            circularity = 4 * np.pi * area / (perimeter * perimeter)
            if circularity < self.thresholds["laser_min_circularity"] / 100.0:
                continue
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            # Brightness check
            if hsv[cy, cx, 2] < self.thresholds["laser_min_brightness"]:
                continue
            score = hsv[cy, cx, 2]
            if score > best_score:
                best_score = score
                best_point = (cx, cy)
        if best_point is not None:
            cv2.circle(img, best_point, self.pixel_radius, (255, 0, 255), -1)
            cv2.putText(img, "Purple", (best_point[0] - 20, best_point[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
            return img, [best_point]
        return img, []


# --------------------------- Main application ---------------------------
if __name__ == "__main__":
    print("Jetson threshold‑tuning detector starting...")
    thresholds = load_thresholds()
    create_trackbars(thresholds)
    # Camera configuration
    CAM_WIDTH, CAM_HEIGHT = 640, 480
    DISPLAY_FPS = 30
    MODE_A4 = 0
    MODE_LASER = 1
    current_mode = MODE_A4
    SWITCH_INTERVAL = 30  # Switch every 30 frames
    # Try to open camera via GStreamer pipeline first
    gst_pipeline = (f"nvarguscamerasrc ! video/x-raw(memory:NVMM), width={CAM_WIDTH}, "
                    f"height={CAM_HEIGHT}, format=NV12, framerate={DISPLAY_FPS}/1 ! "
                    f"nvvidconv ! video/x-raw, format=BGRx ! videoconvert ! video/x-raw, format=BGR ! appsink")
    cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)
    if not cap.isOpened():
        print("GStreamer pipeline failed; trying default camera")
        cap = cv2.VideoCapture(0)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, CAM_WIDTH)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, CAM_HEIGHT)
        cap.set(cv2.CAP_PROP_FPS, DISPLAY_FPS)
    if not cap.isOpened():
        raise RuntimeError("Could not open any camera")
    print(f"Camera opened at {CAM_WIDTH}x{CAM_HEIGHT}@{DISPLAY_FPS}fps")
    detector = PurpleLaserDetector(thresholds, pixel_radius=3)
    serial_comm = SerialComm('/dev/ttyTHS0', 115200, True)
    serial_comm.init()
    frame_count = 0
    fps_start_time = time.time()
    try:
        while True:
            frame_count += 1
            ret, frame = cap.read()
            if not ret:
                continue
            read_trackbars(thresholds)  # Update thresholds from UI
            img_display = frame.copy()
            # Mode switching
            if frame_count % SWITCH_INTERVAL == 0:
                current_mode = MODE_LASER if current_mode == MODE_A4 else MODE_A4
            a4_count = 0
            laser_points = []
            if current_mode == MODE_A4:
                # A4 detection based on HSV thresholds
                hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
                lower_white = np.array([
                    thresholds["white_h_min"], thresholds["white_s_min"], thresholds["white_v_min"]
                ])
                upper_white = np.array([
                    thresholds["white_h_max"], thresholds["white_s_max"], thresholds["white_v_max"]
                ])
                white_mask = cv2.inRange(hsv, lower_white, upper_white)
                kernel = np.ones((3, 3), np.uint8)
                white_mask = cv2.dilate(white_mask, kernel, iterations=1)
                white_mask = cv2.erode(white_mask, kernel, iterations=1)
                contours, _ = cv2.findContours(white_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                candidates = []
                for contour in contours:
                    area = cv2.contourArea(contour)
                    min_area = thresholds["a4_min_area"] * 100  # scale back up
                    max_area = thresholds["a4_max_area"] * 100
                    if not (min_area <= area <= max_area):
                        continue
                    perimeter = cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, 0.03 * perimeter, True)
                    if len(approx) == 4:
                        rect = cv2.minAreaRect(approx)
                        w, h = rect[1]
                        if h == 0 or w == 0:
                            continue
                        aspect_ratio = max(w, h) / min(w, h)
                        if abs(aspect_ratio - 1.414) <= 0.2:
                            candidates.append(approx)
                if candidates:
                    candidates.sort(key=lambda c: cv2.contourArea(c), reverse=True)
                    a4_contour = candidates[0]
                    a4_count = 1
                    pts = a4_contour.reshape(4, 2).astype(np.float32)
                    s = pts.sum(axis=1)
                    tl = pts[np.argmin(s)]
                    br = pts[np.argmax(s)]
                    diff = np.diff(pts, axis=1)
                    tr = pts[np.argmin(diff)]
                    bl = pts[np.argmax(diff)]
                    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
                    cv2.drawContours(img_display, [a4_contour], -1, (0, 255, 0), 2)
                    center_pt = (int((tl[0] + br[0]) / 2), int((tl[1] + br[1]) / 2))
                    cv2.circle(img_display, center_pt, 3, (0, 0, 255), -1)
                    cv2.putText(img_display, "A4 Paper", (int(tl[0]), int(tl[1] - 10)),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                    width = int(np.linalg.norm(tl - tr))
                    height = int(width / 1.414)
                    dst_pts = np.array([
                        [0, 0], [width - 1, 0], [width - 1, height - 1], [0, height - 1]
                    ], dtype=np.float32)
                    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
                    ret2, M_inv = cv2.invert(M)
                    if ret2:
                        tri_center_x = int(width * 0.5)
                        tri_center_y = int(height * 0.5)
                        tri_size = int(min(width, height) * 0.6)
                        tri_h = int(tri_size * 0.866)
                        tri_pts = np.array([
                            [tri_center_x, tri_center_y - tri_h // 2],
                            [tri_center_x - tri_size // 2, tri_center_y + tri_h // 2],
                            [tri_center_x + tri_size // 2, tri_center_y + tri_h // 2]
                        ], dtype=np.float32)
                        generated_points = []
                        for i in range(3):
                            p1, p2 = tri_pts[i], tri_pts[(i + 1) % 3]
                            for j in range(POINTS_PER_EDGE + 1):
                                t = j / POINTS_PER_EDGE
                                x = p1[0] + t * (p2[0] - p1[0])
                                y = p1[1] + t * (p2[1] - p1[1])
                                generated_points.append((x, y))
                        if generated_points:
                            points_array = np.array([generated_points], dtype=np.float32)
                            mapped = cv2.perspectiveTransform(points_array, M_inv)[0]
                            point_count = len(mapped)
                            point_str = ",".join([f"{int(x)},{int(y)}" for x, y in mapped])
                            if mapped.size > 0:
                                serial_comm.send_red_laser_point((mapped[0][0], mapped[0][1]))
                            print(f"发送A4点: M,{point_count},{point_str}")
                            for x, y in mapped:
                                cv2.circle(img_display, (int(x), int(y)), 2, (255, 0, 0), -1)
            else:
                # Laser detection mode
                _, laser_points = detector.detect(img_display)
                if laser_points:
                    x, y = laser_points[0]
                    serial_comm.send_red_laser_point((x, y))
                    print(f"发送激光: P,{x},{y}")
            # Overlay FPS and mode
            elapsed = time.time() - fps_start_time
            fps = int(frame_count / elapsed) if elapsed > 0 else 0
            mode_text = "A4检测" if current_mode == MODE_A4 else "激光检测"
            stats = f"FPS:{fps} | 模式:{mode_text} | A4:{a4_count} | 激光:{len(laser_points)}"
            cv2.putText(img_display, stats, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6,
                        (255, 255, 255), 2)
            cv2.imshow("Detection", img_display)
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                save_thresholds(thresholds)
            elif key == ord('r'):
                thresholds.update(load_thresholds())
                create_trackbars(thresholds)
    except KeyboardInterrupt:
        print("Interrupted by user")
    finally:
        cap.release()
        cv2.destroyAllWindows()
        serial_comm.close()