#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矩形检测器使用示例
演示如何使用RectangleDetector类进行矩形检测
"""

import cv2
import numpy as np
from rectangle_detector import RectangleDetector

def test_with_camera():
    """使用摄像头进行实时矩形检测测试"""
    print("启动摄像头矩形检测测试...")
    
    # 创建矩形检测器
    detector = RectangleDetector()
    
    # 打开摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("无法打开摄像头")
        return
    
    # 设置摄像头分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("按 'q' 退出，按 's' 保存当前帧")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("无法读取摄像头画面")
            break
        
        # 进行矩形检测
        result_img, rectangles = detector.detect(frame)
        
        # 显示检测结果统计
        info_text = f"检测到 {len(rectangles)} 个矩形"
        cv2.putText(result_img, info_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # 显示最大矩形的详细信息
        if rectangles:
            largest = rectangles[0]
            detail_text = f"最大矩形: 面积={largest['area']:.0f}, 长宽比={largest['aspect_ratio']:.2f}"
            cv2.putText(result_img, detail_text, (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        
        # 显示图像
        cv2.imshow('矩形检测', result_img)
        
        # 按键处理
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            cv2.imwrite('rectangle_detection_result.jpg', result_img)
            print("已保存检测结果图像")
    
    cap.release()
    cv2.destroyAllWindows()

def test_with_image(image_path):
    """使用静态图像进行矩形检测测试"""
    print(f"测试图像: {image_path}")
    
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return
    
    # 创建检测器
    detector = RectangleDetector()
    
    # 进行检测
    result_img, rectangles = detector.detect(img)
    
    # 打印检测结果
    print(f"检测到 {len(rectangles)} 个矩形:")
    for i, rect in enumerate(rectangles):
        print(f"  矩形 {i+1}:")
        print(f"    面积: {rect['area']:.0f}")
        print(f"    长宽比: {rect['aspect_ratio']:.2f}")
        print(f"    中心点: {rect['center']}")
        print(f"    四个角点: {rect['corners']}")
    
    # 显示结果
    cv2.imshow('原图', img)
    cv2.imshow('检测结果', result_img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def test_custom_config():
    """测试自定义配置参数"""
    print("测试自定义配置...")
    
    # 创建自定义配置
    custom_config = {
        'min_area': 1000,           # 更大的最小面积
        'max_area': 50000,          # 更小的最大面积
        'target_aspect_ratio': 1.5, # 自定义长宽比
        'aspect_tolerance': 0.3,    # 更宽松的容差
        'contour_color': (255, 0, 0),  # 蓝色轮廓
        'center_color': (0, 255, 0),   # 绿色中心点
        'kernel_size': 5,           # 更大的形态学核
    }
    
    # 创建检测器并应用自定义配置
    detector = RectangleDetector(custom_config)
    
    print("自定义配置已应用:")
    for key, value in custom_config.items():
        print(f"  {key}: {value}")
    
    return detector

def create_test_image():
    """创建一个包含矩形的测试图像"""
    print("创建测试图像...")
    
    # 创建白色背景
    img = np.ones((480, 640, 3), dtype=np.uint8) * 128
    
    # 绘制几个白色矩形
    rectangles = [
        (100, 100, 200, 150),  # x, y, w, h
        (300, 200, 150, 100),
        (450, 50, 120, 180),
    ]
    
    for x, y, w, h in rectangles:
        cv2.rectangle(img, (x, y), (x+w, y+h), (255, 255, 255), -1)  # 白色填充
        cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 0), 2)        # 黑色边框
    
    # 保存测试图像
    cv2.imwrite('test_rectangles.jpg', img)
    print("测试图像已保存: test_rectangles.jpg")
    
    return img

def main():
    """主函数 - 演示各种使用方式"""
    print("=== 矩形检测器使用示例 ===\n")
    
    # 1. 创建测试图像
    test_img = create_test_image()
    
    # 2. 测试基本检测功能
    print("\n1. 基本检测功能测试:")
    detector = RectangleDetector()
    result_img, rectangles = detector.detect(test_img)
    print(f"检测到 {len(rectangles)} 个矩形")
    
    # 3. 测试自定义配置
    print("\n2. 自定义配置测试:")
    custom_detector = test_custom_config()
    
    # 4. 测试静态图像检测
    print("\n3. 静态图像检测测试:")
    test_with_image('test_rectangles.jpg')
    
    # 5. 询问是否进行摄像头测试
    print("\n4. 摄像头实时检测测试:")
    response = input("是否启动摄像头测试? (y/n): ")
    if response.lower() == 'y':
        test_with_camera()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
