import cv2
import numpy as np
from collections import deque
from scipy.interpolate import splprep, splev

# 全局参数配置
class TrajectoryConfig:
    def __init__(self):
        # 二值化参数
        self.median_blur_size = 5
        self.adaptive_block_size = 11
        self.adaptive_c = 2
        self.simple_threshold = 127
        self.use_adaptive = 1  # 0=简单阈值, 1=自适应阈值

        # 形态学操作参数
        self.morph_open_iter = 1
        self.morph_close_iter = 1
        self.morph_kernel_size = 3

        # ROI收缩参数
        self.shrink_factor = 5  # 收缩因子（百分比，0-20）



config = TrajectoryConfig()

def setup_parameter_trackbars():
    """设置参数调整滑动条"""
    cv2.namedWindow('Trajectory Parameters', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('Trajectory Parameters', 400, 500)

    # 二值化参数
    cv2.createTrackbar('Median Blur', 'Trajectory Parameters', config.median_blur_size, 50, lambda x: None)
    cv2.createTrackbar('Simple Thresh', 'Trajectory Parameters', config.simple_threshold, 255, lambda x: None)
    cv2.createTrackbar('Block Size', 'Trajectory Parameters', config.adaptive_block_size, 100, lambda x: None)
    cv2.createTrackbar('C Value', 'Trajectory Parameters', config.adaptive_c, 30, lambda x: None)
    cv2.createTrackbar('Use Adaptive', 'Trajectory Parameters', config.use_adaptive, 1, lambda x: None)

    # 形态学操作参数
    cv2.createTrackbar('Open Iter', 'Trajectory Parameters', config.morph_open_iter, 10, lambda x: None)
    cv2.createTrackbar('Close Iter', 'Trajectory Parameters', config.morph_close_iter, 10, lambda x: None)
    cv2.createTrackbar('Kernel Size', 'Trajectory Parameters', config.morph_kernel_size, 10, lambda x: None)

    # ROI收缩参数
    cv2.createTrackbar('Shrink Factor', 'Trajectory Parameters', config.shrink_factor, 20, lambda x: None)



def update_config_from_trackbars():
    """从滑动条更新配置参数"""
    # 确保中值滤波核大小为奇数
    median_blur = cv2.getTrackbarPos('Median Blur', 'Trajectory Parameters')
    config.median_blur_size = max(1, median_blur | 1)  # 确保为奇数

    # 简单阈值
    config.simple_threshold = cv2.getTrackbarPos('Simple Thresh', 'Trajectory Parameters')

    # 确保块大小为奇数且大于1
    block_size = cv2.getTrackbarPos('Block Size', 'Trajectory Parameters')
    config.adaptive_block_size = max(3, block_size | 1)

    config.adaptive_c = cv2.getTrackbarPos('C Value', 'Trajectory Parameters')
    config.use_adaptive = cv2.getTrackbarPos('Use Adaptive', 'Trajectory Parameters')
    config.morph_open_iter = cv2.getTrackbarPos('Open Iter', 'Trajectory Parameters')
    config.morph_close_iter = cv2.getTrackbarPos('Close Iter', 'Trajectory Parameters')

    # 确保核大小为奇数且大于0
    kernel_size = cv2.getTrackbarPos('Kernel Size', 'Trajectory Parameters')
    config.morph_kernel_size = max(1, kernel_size | 1)

    # ROI收缩参数
    config.shrink_factor = cv2.getTrackbarPos('Shrink Factor', 'Trajectory Parameters')

# 路径规划相关函数
def get_8_neighbors(point, point_set):
    """获取8邻域内的邻居点"""
    x, y = point
    neighbors = []

    # 检查8个方向
    for dx in [-1, 0, 1]:
        for dy in [-1, 0, 1]:
            if dx == 0 and dy == 0:
                continue

            neighbor = (x + dx, y + dy)
            if neighbor in point_set:
                neighbors.append(neighbor)

    return neighbors


def topological_sort_points(points):
    """使用拓扑排序获取有序轨迹点"""
    # 将点转换为元组集合以便快速查找
    point_set = set(tuple(p) for p in points)

    # 如果没有点，返回空列表
    if not point_set:
        return []

    # 找到端点（只有一个邻居的点）
    endpoints = []
    for p in point_set:
        neighbors = get_8_neighbors(p, point_set)
        if len(neighbors) == 1:
            endpoints.append(p)

    # 如果没有端点（闭环），则使用x坐标最小的点
    if not endpoints:
        start_point = min(point_set, key=lambda p: p[0])
    else:
        # 使用x坐标最小的端点作为起点
        start_point = min(endpoints, key=lambda p: p[0])

    # 使用BFS遍历点
    visited = set()
    queue = deque([start_point])
    sorted_points = []

    while queue:
        current = queue.popleft()
        if current in visited:
            continue

        visited.add(current)
        sorted_points.append(current)

        # 获取当前点的邻居
        neighbors = get_8_neighbors(current, point_set)

        # 按方向连续性排序邻居（优先选择与上一方向一致的邻居）
        if len(sorted_points) > 1:
            prev_point = sorted_points[-2]
            dx = current[0] - prev_point[0]
            dy = current[1] - prev_point[1]

            # 优先选择方向一致的邻居
            neighbors.sort(key=lambda n: abs((n[0] - current[0]) - dx) + abs((n[1] - current[1]) - dy))

        # 添加未访问的邻居
        for neighbor in neighbors:
            if neighbor not in visited:
                queue.append(neighbor)

    # 转换为numpy数组
    return np.array([list(p) for p in sorted_points])


def interpolate_trajectory(points, num_points=100):
    """对轨迹点进行样条插值"""
    if points is None or len(points) < 4:
        return points

    # 添加0.1%的随机抖动避免重复x值
    jitter = np.random.normal(0, 0.001, points.shape)
    points = points.astype(float) + jitter

    # 样条插值
    tck, u = splprep(points.T, u=None, s=0.0, per=0)
    u_new = np.linspace(u.min(), u.max(), num_points)
    x_new, y_new = splev(u_new, tck, der=0)

    return np.column_stack((x_new, y_new))


def extract_trajectory_from_roi(roi_image):
    """从ROI区域提取轨迹（使用可调节参数）"""
    if roi_image is None or roi_image.size == 0:
        return None

    # 转换为灰度图
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)

    # 中值滤波减少噪声（使用可调节参数）
    filtered = cv2.medianBlur(gray, config.median_blur_size)

    # 二值化处理（可选择简单阈值或自适应阈值）
    if config.use_adaptive == 1:
        # 自适应阈值处理
        thresh = cv2.adaptiveThreshold(
            filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY_INV, config.adaptive_block_size, config.adaptive_c
        )
    else:
        # 简单阈值处理
        _, thresh = cv2.threshold(filtered, config.simple_threshold, 255, cv2.THRESH_BINARY_INV)





    # 形态学操作（使用可调节参数）
    kernel = np.ones((config.morph_kernel_size, config.morph_kernel_size), np.uint8)
    opened = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=config.morph_open_iter)
    closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel, iterations=config.morph_close_iter)





    # 骨架提取
    skeleton = cv2.ximgproc.thinning(closed, thinningType=cv2.ximgproc.THINNING_GUOHALL)



    # 找到最大连通区域进行路径规划
    skeleton_pixels = np.sum(skeleton > 0)
    if skeleton_pixels > 0:
        # 查找骨架中的连通组件
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(skeleton, connectivity=8)

        if num_labels > 1:  # 除了背景外还有其他组件
            # 找到面积最大的连通区域（排除背景标签0）
            areas = stats[1:, cv2.CC_STAT_AREA]  # 排除背景
            largest_component_idx = np.argmax(areas) + 1  # +1因为排除了背景
            largest_area = areas[largest_component_idx - 1]





            # 创建只包含最大连通区域的骨架
            largest_skeleton = np.zeros_like(skeleton)
            largest_skeleton[labels == largest_component_idx] = 255

            # 从最大连通区域中提取点坐标
            points = np.column_stack(np.where(largest_skeleton > 0))

            # 将坐标转换为(x, y)格式
            if points.size > 0:
                points = points[:, [1, 0]]  # 交换列顺序

                # 使用拓扑排序获取有序轨迹点
                sorted_points = topological_sort_points(points)
                return sorted_points, largest_skeleton, thresh, closed
            else:

                return None, skeleton, thresh, closed
        else:

            # 从骨架中提取所有点坐标
            points = np.column_stack(np.where(skeleton > 0))

            # 将坐标转换为(x, y)格式
            if points.size > 0:
                points = points[:, [1, 0]]  # 交换列顺序

                # 使用拓扑排序获取有序轨迹点
                sorted_points = topological_sort_points(points)
                return sorted_points, skeleton, thresh, closed
            else:

                return None, skeleton, thresh, closed
    else:

        return None, skeleton, thresh, closed


# 打开摄像头
cap = cv2.VideoCapture(0)

if not cap.isOpened():
    pass
else:
    # 设置参数调整窗口
    setup_parameter_trackbars()


    # 定义最小轮廓面积
    min_contour_area = 1000
    max_contour_area = 30000

    while True:
        # 更新参数配置
        update_config_from_trackbars()

        # 读取一帧图像
        ret, frame = cap.read()

        if not ret:
            break

        # 定义缩放比例
        scale_percent = 50
        width = int(frame.shape[1] * scale_percent / 100)
        height = int(frame.shape[0] * scale_percent / 100)
        dim = (width, height)

        # 缩放图像
        resized_image = cv2.resize(frame, dim, interpolation=cv2.INTER_AREA)
        # 转换为灰度图像
        gray_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2GRAY)
        # 进行二值化处理
        _, binary_image = cv2.threshold(gray_image, 46, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # 指定要显示的边数
        target_sides = 4
        approximated_contours = []
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            if area > min_contour_area and area < max_contour_area:
                # 计算轮廓周长

                # 进行多边形逼近
                epsilon = 0.03 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                if len(approx) == target_sides:
                    approximated_contours.append((approx, area))

        # 按面积排序，面积大的为外框，面积小的为内框
        approximated_contours.sort(key=lambda x: x[1], reverse=True)

        # 在原始缩放图像上绘制指定边数的轮廓
        contour_image = resized_image.copy()
        
        # 如果检测到至少两个矩形（外框和内框）
        if len(approximated_contours) >= 2:
            outer_rect = approximated_contours[0][0]  # 外框
            inner_rect = approximated_contours[1][0]  # 内框

            # 绘制外框（红色）
            cv2.drawContours(contour_image, [outer_rect], -1, (0, 0, 255), 2)
            # 绘制内框（绿色）
            cv2.drawContours(contour_image, [inner_rect], -1, (0, 255, 0), 2)

            # 创建ROI区域（内框内部）- 使用111.py的向量收缩算法
            def shrink_quadrilateral(points, shrink_factor=0.1):
                """根据四个顶点向内收缩四边形（参考111.py）"""
                if points is None or len(points) != 4:
                    return None

                # 计算中心点
                center = np.mean(points, axis=0)

                # 计算每个点向中心移动的方向向量
                shrunk_points = []
                for point in points:
                    # 计算点到中心的方向向量
                    direction = center - point
                    # 按比例收缩
                    shrunk_point = point + shrink_factor * direction
                    shrunk_points.append(shrunk_point)

                return np.array(shrunk_points, dtype=np.float32)

            # 将inner_rect转换为正确的格式
            inner_points = np.array([point[0] for point in inner_rect], dtype=np.float32)

            # 使用向量收缩算法，向内收缩（使用可调节参数）
            shrink_factor = config.shrink_factor / 100.0  # 转换为小数（0-0.2）
            shrunk_inner_rect = shrink_quadrilateral(inner_points, shrink_factor)



            # 创建原始掩码
            mask_original = np.zeros(resized_image.shape[:2], dtype=np.uint8)
            cv2.fillPoly(mask_original, [inner_rect], 255)

            # 创建收缩后的掩码
            mask_shrunk = np.zeros(resized_image.shape[:2], dtype=np.uint8)
            if shrunk_inner_rect is not None:
                shrunk_rect_int = np.array([shrunk_inner_rect], dtype=np.int32)
                cv2.fillPoly(mask_shrunk, shrunk_rect_int, 255)
            else:
                mask_shrunk = mask_original.copy()


            # 提取ROI区域（使用收缩后的掩码）
            roi = cv2.bitwise_and(resized_image, resized_image, mask=mask_shrunk)

            # 统计ROI区域的有效像素
            roi_pixels = np.sum(mask_shrunk > 0)




            # 从ROI区域提取轨迹（使用收缩后的正确ROI区域）
            trajectory_result = extract_trajectory_from_roi(roi)

            if trajectory_result is not None:
                sorted_points, skeleton, binary_thresh, morphology_result = trajectory_result

                if sorted_points is not None and len(sorted_points) > 0:
                    # 对ROI内的轨迹进行插值平滑
                    if len(sorted_points) >= 4:
                        interpolated_points = interpolate_trajectory(sorted_points, 100)

                        # 创建ROI轨迹显示图像（使用正确的ROI区域）
                        roi_trajectory = roi.copy()

                        # 在ROI图像上绘制插值后的轨迹
                        for i in range(1, len(interpolated_points)):
                            pt1 = (int(interpolated_points[i-1, 0]), int(interpolated_points[i-1, 1]))
                            pt2 = (int(interpolated_points[i, 0]), int(interpolated_points[i, 1]))
                            cv2.line(roi_trajectory, pt1, pt2, (0, 255, 255), 2)  # 青色轨迹线

                        # 在ROI图像上标记起点和终点
                        start_pt = (int(interpolated_points[0, 0]), int(interpolated_points[0, 1]))
                        end_pt = (int(interpolated_points[-1, 0]), int(interpolated_points[-1, 1]))
                        cv2.circle(roi_trajectory, start_pt, 3, (0, 255, 0), -1)  # 绿色起点
                        cv2.circle(roi_trajectory, end_pt, 3, (0, 0, 255), -1)    # 红色终点

                        # 显示带轨迹的ROI区域
                        cv2.imshow('ROI with Trajectory', roi_trajectory)


                    else:
                        # 如果点数不够插值，直接绘制原始轨迹点
                        roi_trajectory = roi.copy()
                        for i in range(1, len(sorted_points)):
                            pt1 = (int(sorted_points[i-1, 0]), int(sorted_points[i-1, 1]))
                            pt2 = (int(sorted_points[i, 0]), int(sorted_points[i, 1]))
                            cv2.line(roi_trajectory, pt1, pt2, (0, 255, 255), 2)

                        cv2.imshow('ROI with Trajectory', roi_trajectory)

                # 显示骨架图像
                if skeleton is not None:
                    skeleton_display = cv2.resize(skeleton, (200, 150))
                    cv2.imshow('Skeleton', skeleton_display)

            # 在ROI区域添加半透明蓝色覆盖层以突出显示
            roi_overlay = contour_image.copy()
            cv2.fillPoly(roi_overlay, [inner_rect], (255, 0, 0))  # 蓝色填充
            contour_image = cv2.addWeighted(contour_image, 0.8, roi_overlay, 0.1, 0)

            # 标注外框
            outer_center_x = int(sum([p[0][0] for p in outer_rect]) / 4)
            outer_center_y = int(sum([p[0][1] for p in outer_rect]) / 4)
            cv2.putText(contour_image, "Outer Frame", (outer_center_x-40, outer_center_y-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

            # 标注内框ROI
            inner_center_x = int(sum([p[0][0] for p in inner_rect]) / 4)
            inner_center_y = int(sum([p[0][1] for p in inner_rect]) / 4)
            cv2.putText(contour_image, "ROI Area", (inner_center_x-30, inner_center_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)


        
        else:
            # 如果只有一个矩形，正常绘制
            for i, (approx, area) in enumerate(approximated_contours):
                color = (0, 0, 255) if i == 0 else (0, 255, 0)
                cv2.drawContours(contour_image, [approx], -1, color, 2)


        # 显示带有指定边数轮廓的图像
        cv2.imshow('Contour Image with Specified Sides', contour_image)

        # 按 'Esc' 键退出循环
        if cv2.waitKey(1) == 27:
            break

    # 释放摄像头并关闭所有窗口
    cap.release()
    cv2.destroyAllWindows()
    
