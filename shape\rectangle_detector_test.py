import cv2
import numpy as np

class RectangleDetectorTest:
    def __init__(self):
        # ROI矩形检测参数 - 与track_detection.py完全相同
        self.min_rect_area = 1000      # 最小矩形面积
        self.max_rect_area = 100000     # 最大矩形面积
        self.rect_aspect_ratio_min = 0.3  # 矩形最小宽高比
        self.rect_aspect_ratio_max = 3.0  # 矩形最大宽高比

        # 内外框检测参数
        self.detect_inner_outer = True  # 是否检测内外框
        self.min_inner_ratio = 0.3      # 内框最小尺寸比例（相对于外框）
        self.max_inner_ratio = 0.9      # 内框最大尺寸比例（相对于外框）
        self.frame_thickness_ratio = 0.05  # 框线厚度比例

    def detect_rectangles(self, img):
        """检测矩形ROI区域 - 与track_detection.py完全相同的算法"""
        # 图像预处理 - 使用更强的预处理
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # 边缘检测 - 调整参数以更好地检测矩形
        edges = cv2.Canny(blurred, 50, 150)

        # 形态学操作 - 连接断开的边缘
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        # 查找轮廓 - 使用RETR_TREE获取层次结构
        contours, hierarchy = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        print(f"检测到 {len(contours)} 个轮廓")

        # 如果启用内外框检测，分析轮廓层次结构
        if self.detect_inner_outer and hierarchy is not None:
            return self.detect_inner_outer_rectangles(contours, hierarchy)
        else:
            return self.detect_single_rectangles(contours)


    def detect_single_rectangles(self, contours):
        """单一矩形检测（原始方法）"""
        rectangles = []

        for i, contour in enumerate(contours):
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            print(f"轮廓 {i}: 面积 = {area}")

            # 面积过滤
            if area < self.min_rect_area or area > self.max_rect_area:
                print(f"  ❌ 面积不符合要求 ({self.min_rect_area}-{self.max_rect_area})")
                continue

            # 计算轮廓周长
            perimeter = cv2.arcLength(contour, True)

            # 多边形逼近 - 使用更精确的逼近
            epsilon = 0.02 * perimeter
            approx = cv2.approxPolyDP(contour, epsilon, True)

            print(f"  轮廓 {i}: 顶点数 = {len(approx)}")

            # 检查是否为四边形（矩形）
            if len(approx) == 4:
                # 计算边界矩形
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = float(w) / h

                print(f"  矩形候选: 位置({x},{y}), 尺寸({w}x{h}), 宽高比={aspect_ratio:.2f}")

                # 宽高比检查
                if self.rect_aspect_ratio_min <= aspect_ratio <= self.rect_aspect_ratio_max:
                    # 计算轮廓的紧密度（面积与边界矩形面积的比值）
                    rect_area = w * h
                    compactness = area / rect_area if rect_area > 0 else 0

                    print(f"    紧密度: {compactness:.3f}")

                    # 只接受相对紧密的矩形（避免奇怪形状）
                    if compactness > 0.7:  # 至少70%的填充度
                        rectangles.append({
                            'contour': contour,
                            'approx': approx,
                            'bbox': (x, y, w, h),
                            'area': area,
                            'aspect_ratio': aspect_ratio,
                            'compactness': compactness,
                            'type': 'single'
                        })
                        print(f"    ✅ 矩形已添加: 面积={area:.0f}, 紧密度={compactness:.3f}")
                    else:
                        print(f"    ❌ 紧密度不足 (需要>0.7)")
                else:
                    print(f"    ❌ 宽高比不符合 (需要{self.rect_aspect_ratio_min}-{self.rect_aspect_ratio_max})")
            else:
                print(f"  ❌ 不是四边形 (顶点数: {len(approx)})")

        # 按面积排序，返回最大的矩形作为主ROI
        rectangles.sort(key=lambda x: x['area'], reverse=True)
        print(f"\n最终检测到 {len(rectangles)} 个有效矩形")

        return rectangles

    def detect_inner_outer_rectangles(self, contours, hierarchy):
        """内外框矩形检测"""

        # 分析层次结构，找到外框和内框的关系
        outer_rectangles = []
        inner_rectangles = []

        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)

            # 面积过滤
            if area < self.min_rect_area or area > self.max_rect_area:
                continue

            # 检查是否为矩形
            perimeter = cv2.arcLength(contour, True)
            epsilon = 0.02 * perimeter
            approx = cv2.approxPolyDP(contour, epsilon, True)

            if len(approx) == 4:
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = float(w) / h

                if self.rect_aspect_ratio_min <= aspect_ratio <= self.rect_aspect_ratio_max:
                    rect_area = w * h
                    compactness = area / rect_area if rect_area > 0 else 0

                    if compactness > 0.7:
                        rect_info = {
                            'contour': contour,
                            'approx': approx,
                            'bbox': (x, y, w, h),
                            'area': area,
                            'aspect_ratio': aspect_ratio,
                            'compactness': compactness,
                            'hierarchy_index': i,
                            'parent': hierarchy[0][i][3],  # 父轮廓索引
                            'child': hierarchy[0][i][2],   # 子轮廓索引
                        }

                        # 判断是外框还是内框
                        if rect_info['parent'] == -1:  # 没有父轮廓，是外框
                            rect_info['type'] = 'outer'
                            outer_rectangles.append(rect_info)
                            print(f"✅ 外框: 位置({x},{y}), 尺寸({w}x{h}), 面积={area:.0f}")
                        else:  # 有父轮廓，是内框
                            rect_info['type'] = 'inner'
                            inner_rectangles.append(rect_info)
                            print(f"✅ 内框: 位置({x},{y}), 尺寸({w}x{h}), 面积={area:.0f}")

        # 匹配内外框对
        frame_pairs = []
        for outer in outer_rectangles:
            for inner in inner_rectangles:
                if self.is_inner_frame_valid(outer, inner):
                    frame_pairs.append({
                        'outer': outer,
                        'inner': inner,
                        'type': 'frame_pair'
                    })
                    print(f"🔗 匹配内外框对: 外框面积={outer['area']:.0f}, 内框面积={inner['area']:.0f}")

        # 合并所有结果
        all_rectangles = outer_rectangles + inner_rectangles + frame_pairs
        all_rectangles.sort(key=lambda x: x.get('area', x.get('outer', {}).get('area', 0)), reverse=True)

        print(f"\n检测结果: {len(outer_rectangles)}个外框, {len(inner_rectangles)}个内框, {len(frame_pairs)}个内外框对")

        return all_rectangles

    def is_inner_frame_valid(self, outer, inner):
        """验证内框是否是外框的有效内框"""
        ox, oy, ow, oh = outer['bbox']
        ix, iy, iw, ih = inner['bbox']

        # 检查内框是否在外框内部
        if not (ox < ix and oy < iy and ox + ow > ix + iw and oy + oh > iy + ih):
            return False

        # 检查尺寸比例
        size_ratio_w = iw / ow
        size_ratio_h = ih / oh

        if (self.min_inner_ratio <= size_ratio_w <= self.max_inner_ratio and
            self.min_inner_ratio <= size_ratio_h <= self.max_inner_ratio):
            return True

        return False

    def visualize_detection_process(self, img):
        """可视化检测过程的各个步骤"""
        # 1. 原图
        original = img.copy()
        
        # 2. 灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 3. 模糊后
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 4. 边缘检测
        edges = cv2.Canny(blurred, 50, 150)
        
        # 5. 形态学处理后
        kernel = np.ones((3, 3), np.uint8)
        edges_processed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        return {
            'original': original,
            'gray': gray,
            'blurred': blurred,
            'edges': edges,
            'edges_processed': edges_processed
        }

    def visualize_results(self, img, rectangles):
        """可视化检测结果 - 支持内外框显示"""
        result_img = img.copy()

        # 绘制所有检测到的矩形
        for i, rect in enumerate(rectangles):
            rect_type = rect.get('type', 'single')

            if rect_type == 'frame_pair':
                # 绘制内外框对
                self.draw_frame_pair(result_img, rect, i)
            else:
                # 绘制单个矩形
                self.draw_single_rectangle(result_img, rect, i)

        # 添加总体信息
        cv2.putText(result_img, f"Found {len(rectangles)} rectangles", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        return result_img

    def draw_single_rectangle(self, img, rect, index):
        """绘制单个矩形"""
        x, y, w, h = rect['bbox']
        rect_type = rect.get('type', 'single')

        # 根据类型选择颜色
        if rect_type == 'outer':
            color = (0, 0, 255)  # 红色 - 外框
            label = f"Outer #{index+1}"
        elif rect_type == 'inner':
            color = (0, 255, 0)  # 绿色 - 内框
            label = f"Inner #{index+1}"
        else:
            # 不同优先级的单一矩形
            if index == 0:
                color = (0, 255, 255)  # 黄色 - 最佳矩形
                thickness = 3
            elif index == 1:
                color = (255, 0, 255)  # 紫色 - 次选
                thickness = 2
            else:
                color = (255, 0, 0)  # 蓝色 - 其他
                thickness = 1
            label = f"#{index+1}"

        thickness = 2

        # 绘制矩形框
        cv2.rectangle(img, (x, y), (x+w, y+h), color, thickness)

        # 添加标签
        cv2.putText(img, label, (x, y-10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        # 添加详细信息
        info_text = f"A:{rect['area']:.0f} AR:{rect['aspect_ratio']:.2f} C:{rect['compactness']:.2f}"
        cv2.putText(img, info_text, (x, y+h+15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

    def draw_frame_pair(self, img, frame_pair, index):
        """绘制内外框对"""
        outer = frame_pair['outer']
        inner = frame_pair['inner']

        # 绘制外框 - 红色粗线
        ox, oy, ow, oh = outer['bbox']
        cv2.rectangle(img, (ox, oy), (ox+ow, oy+oh), (0, 0, 255), 3)
        cv2.putText(img, f"Outer Frame #{index+1}", (ox, oy-25),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

        # 绘制内框 - 绿色粗线
        ix, iy, iw, ih = inner['bbox']
        cv2.rectangle(img, (ix, iy), (ix+iw, iy+ih), (0, 255, 0), 3)
        cv2.putText(img, f"Inner Frame #{index+1}", (ix, iy-10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # 添加连接线
        cv2.line(img, (ox, oy), (ix, iy), (255, 255, 0), 1)
        cv2.line(img, (ox+ow, oy+oh), (ix+iw, iy+ih), (255, 255, 0), 1)

        # 添加详细信息
        info_text = f"Pair: Outer={outer['area']:.0f}, Inner={inner['area']:.0f}"
        cv2.putText(img, info_text, (ox, oy+oh+30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)

def test_with_camera():
    """使用摄像头进行实时测试"""
    detector = RectangleDetectorTest()
    cap = cv2.VideoCapture(0)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    print("=== 矩形检测测试系统 ===")
    print("按键控制:")
    print("  'q' - 退出程序")
    print("  's' - 显示/隐藏处理步骤")
    print("  'i' - 切换内外框检测模式")
    print("  'p' - 暂停/继续")
    print("=" * 40)

    show_process = False
    paused = False
    frame_count = 0

    while True:
        if not paused:
            ret, frame = cap.read()
            if not ret:
                break
            frame_count += 1

        # 检测矩形
        rectangles = detector.detect_rectangles(frame)
        
        # 可视化结果
        result_img = detector.visualize_results(frame, rectangles)
        cv2.imshow('Rectangle Detection Test', result_img)
        
        # 显示处理步骤（可选）
        if show_process:
            process_imgs = detector.visualize_detection_process(frame)
            cv2.imshow('1. Gray', process_imgs['gray'])
            cv2.imshow('2. Blurred', process_imgs['blurred'])
            cv2.imshow('3. Edges', process_imgs['edges'])
            cv2.imshow('4. Processed Edges', process_imgs['edges_processed'])
        
        # 键盘控制
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            show_process = not show_process
            print(f"处理步骤显示: {'开启' if show_process else '关闭'}")
            if not show_process:
                cv2.destroyWindow('1. Gray')
                cv2.destroyWindow('2. Blurred')
                cv2.destroyWindow('3. Edges')
                cv2.destroyWindow('4. Processed Edges')
        elif key == ord('i'):
            detector.detect_inner_outer = not detector.detect_inner_outer
            print(f"内外框检测模式: {'开启' if detector.detect_inner_outer else '关闭'}")
            if detector.detect_inner_outer:
                print("  - 将检测内外框对和层次结构")
                print("  - 红色框: 外框, 绿色框: 内框, 黄色线: 连接")
            else:
                print("  - 将使用单一矩形检测模式")
        elif key == ord('p'):
            paused = not paused
            print(f"{'暂停' if paused else '继续'}")

    cap.release()
    cv2.destroyAllWindows()

def test_with_image(image_path):
    """使用静态图像进行测试"""
    detector = RectangleDetectorTest()
    
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return
    
    print(f"=== 测试图像: {image_path} ===")
    
    # 检测矩形
    rectangles = detector.detect_rectangles(img)
    
    # 可视化结果
    result_img = detector.visualize_results(img, rectangles)
    
    # 显示处理步骤
    process_imgs = detector.visualize_detection_process(img)
    
    # 显示所有窗口
    cv2.imshow('Original', img)
    cv2.imshow('Result', result_img)
    cv2.imshow('Gray', process_imgs['gray'])
    cv2.imshow('Edges', process_imgs['edges'])
    cv2.imshow('Processed Edges', process_imgs['edges_processed'])
    
    print("按任意键关闭...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 如果提供了图像路径，测试静态图像
        test_with_image(sys.argv[1])
    else:
        # 否则使用摄像头实时测试
        test_with_camera()
